"use client";

import RatingStars from "@/components/common/RatingStars";
import COLORS from "@/constants/colors";

import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
} from "@/components/common/Accordion";
import HorizontalScrollCards from "@/components/common/HorizontalScrollCards";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { ClubsQuery } from "@/generated/graphql";
import { useHandleToggleAccordion } from "@/hooks/use-utils";
import { cn, DAYS, formatMilitaryTime } from "@/lib/utils";
import ICONS from "@/public/icons";
import { format } from "date-fns";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import ClubDetail from ".";

export const revalidate = 3600;

type ClubDetailSectionProps = {
  club: ClubsQuery["clubs"]["docs"][0];
  otherClubs: ClubsQuery["clubs"];
};

const today = new Date();
const dayOfWeek = format(today, "EEEE").toUpperCase();

export default function ClubDetailSection({
  club,
  otherClubs,
}: ClubDetailSectionProps) {
  const clubData = club;
  const [selectedImage, setSelectedImage] = useState<number>(0);
  const { activeIndex, handleToggle } = useHandleToggleAccordion(0);

  const heroImages = [...new Set([clubData.coverImage, ...clubData.images])];

  const [expanded, setExpanded] = useState(false);

  const handleReadMore = () => {
    setExpanded((prev) => !prev);
  };

  return (
    <section>
      <div className="w-full lg:pt-[80px] lg:pb-[105px]  bg-transparent lg:bg-gradient-to-br lg:from-text lg:from-55% lg:to-secondary">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between px-4 py-5 container md:gap-[20px] xl:gap-[58px]">
          <div className="flex flex-col gap-3 xl:gap-3.5 mb-5 w-full overflow-x-auto hide-scrollbar">
            <Image
              src={heroImages[selectedImage] || "/svg/cheers.svg"}
              width={365}
              height={250}
              alt="club1"
              className="rounded-[18px] w-full h-[250px] lg:h-[400px] object-cover transition-all duration-300 ease-in-out aspect-square"
              quality={100}
              loading="eager"
              priority={true}
            />
            {clubData.images.length > 1 ? (
              <div className="w-full">
                <HorizontalScrollCards>
                  {heroImages.map((image, idx) => (
                    <button
                      key={idx}
                      onClick={() => setSelectedImage(idx)}
                      className={cn(
                        "bg-transparent cursor-pointer overflow-hidden rounded-[18px] min-w-[85px] min-h-[92px] lg:min-w-[144px] lg:min-h-[156px] ",
                        idx === selectedImage &&
                          "ring-[3px] lg:ring-[6px] ring-primary"
                      )}
                    >
                      <Image
                        src={image || "/svg/tempLogo.svg"}
                        width={85}
                        height={92}
                        alt={`bar-image-${idx}`}
                        className="min-w-[85px] min-h-[92px] lg:min-w-[144px] lg:min-h-[156px] object-cover aspect-square"
                        quality={100}
                        loading="eager"
                        priority={true}
                      />
                    </button>
                  ))}
                </HorizontalScrollCards>
              </div>
            ) : (
              <div className="w-full h-[92px] lg:h-[156px]" />
            )}
          </div>
          <div className="w-full">
            <RatingStars
              className="bg-transparent border-0 lg:hidden"
              ratingValue={clubData?.rating || 0}
              width={23}
              height={22}
              textStyles="text-text  text-[24px] font-bold ml-2"
            />
            <RatingStars
              className="bg-transparent border-0 lg:mb-8 hidden lg:block"
              ratingValue={clubData?.rating || 0}
              width={35}
              height={32}
              textStyles="text-footerbg  text-[24px] font-bold ml-2"
            />
            <div className="text-[32px] font-bold leading-9 text-text lg:text-footerbg sm:text-3xl xl:text-6xl xl:leading-16">
              {clubData?.name}
            </div>
            <div className="flex flex-row items-center w-full mt-2 gap-2 mb-2.5 lg:mt-[50px] lg:mb-5">
              <div className="min-w-[38px] min-h-[38px] rounded-full bg-footerbg flex items-center justify-center">
                <ICONS.PinIcon
                  width={22}
                  height={25}
                  color={COLORS.LIGHT.TEXT}
                  className="lg:hidden"
                />
                <ICONS.PinIcon
                  width={28}
                  height={32}
                  color={COLORS.LIGHT.TEXT}
                  className="hidden lg:flex"
                />
              </div>
              <div className="text-sm font-medium text-text lg:text-footerbg lg:text-xl w-full">
                {clubData?.address?.address}
              </div>
            </div>
            <div className="w-full flex flex-row items-center gap-1.5 flex-wrap mb-[28px]">
              {clubData?.tags?.slice(0, 2).map((tag, idx) => (
                <button
                  key={idx}
                  className="text-text text-xl lg:text-2xl font-medium bg-muted-primary rounded-[4px] px-2 py-1 flex items-center justify-center"
                >
                  {tag?.name}
                </button>
              ))}
              {clubData?.tags?.length > 2 && (
                <Tooltip>
                  <TooltipTrigger>
                    <p className="text-text text-xl lg:text-2xl font-medium bg-transparent ring-2 ring-muted-primary rounded-[4px] px-2 py-0.5 flex items-center justify-center">
                      +{clubData.tags.length - 2}
                    </p>
                  </TooltipTrigger>
                  <TooltipContent className="flex flex-row items-center gap-1 py-2 bg-text">
                    {clubData?.tags?.map((tag, idx) => (
                      <p
                        key={idx}
                        className="text-xs lg:text-sm font-medium bg-muted-primary rounded-[4px] px-2 py-1 flex items-center justify-center"
                      >
                        {tag?.name}
                      </p>
                    ))}
                  </TooltipContent>
                </Tooltip>
              )}
            </div>

            <div className="flex flex-row items-center gap-2 text-xl lg:text-2xl text-secondary lg:text-muted-primary font-bold mb-[25px] lg:mb-[30px]">
              <div>Contact:</div>
              <Link
                className="hover:underline hover:underline-offset-2 text-secondary lg:text-muted-primary"
                href={`tel:${clubData?.contact?.countryCode} ${clubData?.contact?.phone}`}
              >
                {clubData?.contact?.countryCode} {clubData?.contact?.phone}
              </Link>
            </div>
            <Tooltip>
              <TooltipTrigger>
                <div className="px-10 btn-gradient-4 sm:px-[28px] py-[13px] mb-[33px] lg:text-xl font-bold font-montserrat leading-5 text-white rounded-full cursor-pointer ">
                  BOOK A TABLE
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-sm font-medium text-primary">Coming Soon</p>
              </TooltipContent>
            </Tooltip>
          </div>
        </div>
      </div>

      {/* CLUB DESCRIPTION */}
      <div
        className={cn(
          "w-full text-sm md:text-xl leading-[29px] text-text md:mt-[60px] lg:mb-10 mb-6 container overflow-hidden transition-all duration-500 ease-in-out",
          expanded ? "max-h-[1000px]" : "max-h-[364px] lg:max-h-[170px]"
        )}
      >
        {clubData?.description}
      </div>

      {clubData?.description?.length > 0 && (
        <div className="w-full flex items-center justify-center mb-4 lg:mb-8">
          <button
            onClick={handleReadMore}
            className="text-sm font-medium text-secondary leading-7 sm:text-xl cursor-pointer hover:underline hover:underline-offset-2"
          >
            {expanded ? "Show less" : "Read more..."}
          </button>
        </div>
      )}

      {/* DETAILS */}
      <div className="w-full grid grid-cols-1 lg:grid-cols-2 gap-16 container px-4 mb-[10px] lg:mb-[30px]">
        {/* MENU */}
        {clubData.menu?.sections && (
          <div
            className={cn(
              "w-full h-full flex flex-col gap-4",
              activeIndex === null && "h-full max-h-full",
              clubData.menu?.sections.length === 0 && "hidden"
            )}
          >
            <h2 className="font-bold text-primary text-xl sm:text-2xl leading-5">
              Menu
            </h2>
            <div className="w-full h-full overflow-y-auto hide-scrollbar flex flex-col gap-4 mt-6 py-1">
              {clubData?.menu?.sections.map((menu, idx) => (
                <Accordion
                  key={idx}
                  className="w-full flex flex-col gap-2 lg:gap-3"
                >
                  <div>
                    <AccordionTrigger
                      className="justify-between bg-footerbg rounded-[8px] py-2 text-secondary text-base lg:text-xl"
                      onClick={handleToggle(idx)}
                      isActive={activeIndex === idx}
                    >
                      {menu.name}
                      {activeIndex === idx ? (
                        <ICONS.ChevronUpIcon
                          width={13}
                          height={7}
                          color={COLORS.LIGHT.SECONDARY}
                        />
                      ) : (
                        <ICONS.ChevronDownIcon
                          width={13}
                          height={7}
                          color={COLORS.LIGHT.SECONDARY}
                        />
                      )}
                    </AccordionTrigger>

                    <div
                      className={cn(
                        "max-h-[230px] overflow-y-auto scrollbar-theme relative"
                      )}
                    >
                      {activeIndex === idx && (
                        <div className="flex flex-row items-center justify-between w-full py-1.5 lg:py-2 pl-2 pr-4 lg:px-4 bg-text sticky top-0 rounded-b-[8px] z-50 ">
                          <div className="flex-1 max-w-[70%] lg:max-w-[80%]">
                            <ICONS.BeerJugIcon
                              width={15}
                              height={15}
                              color={COLORS.LIGHT.MUTED_PRIMARY}
                              className="w-[15px] h-[15px] lg:w-[23px] lg:h-[24px]"
                            />
                          </div>
                          <div className="text-xs sm:text-base xl:text-xl font-medium text-muted-primary flex flex-1 max-w-[20%] sm:max-w-[30%] items-center justify-center">
                            Standard
                          </div>
                        </div>
                      )}
                      <AccordionItem isActive={activeIndex === idx}>
                        {menu.items.map((item, itemIdx) => (
                          <div
                            key={itemIdx}
                            className={cn(
                              "flex flex-row justify-between p-1.5 lg:px-4 mx-1",
                              menu.items.length - 1 === itemIdx
                                ? "border-b-0"
                                : "border-b border-secondary/30"
                            )}
                          >
                            <div className="text-base xl:text-xl font-bold text-secondary flex-1 max-w-[75%] lg:max-w-[80%]">
                              {item.name}
                            </div>
                            <div className="text-xs sm:text-base xl:text-xl font-medium text-secondary truncate  flex flex-1 max-w-[20%] pl-3">
                              {clubData?.menu?.currency} {item.price}
                            </div>
                          </div>
                        ))}
                      </AccordionItem>
                    </div>
                  </div>
                </Accordion>
              ))}
            </div>
          </div>
        )}

        {/* OPENING HOURS */}
        {clubData.businessHours && (
          <div className="w-full flex flex-col gap-4">
            <h2 className="font-bold text-primary text-xl sm:text-2xl leading-5">
              Opening hours:
            </h2>
            <div className="bg-transparent sm:bg-backgroundlight py-1 sm:py-[28px] sm:px-[25px] sm:mt-[27px] mb-8 sm:mb-0">
              {DAYS.map((weekDay, idx) => (
                <div
                  key={idx}
                  className={cn(
                    "flex flex-row items-center justify-between py-[7px] sm:py-[12px] border-b border-secondary",
                    idx === DAYS.length - 1 && "border-b-0"
                  )}
                >
                  <div
                    className={cn(
                      "text-sm sm:text-xl leading-5 text-secondary",
                      weekDay === dayOfWeek && "font-bold"
                    )}
                  >
                    {weekDay}
                  </div>
                  {(() => {
                    const daySchedule = clubData?.businessHours?.schedule.find(
                      (day) => day.day === weekDay
                    );

                    if (!daySchedule || daySchedule.timings.length === 0) {
                      return (
                        <span
                          className={cn(
                            "text-sm sm:text-xl text-red-400",
                            weekDay === dayOfWeek && "font-bold"
                          )}
                        >
                          CLOSED
                        </span>
                      );
                    }

                    return (
                      <span
                        className={cn(
                          "text-sm sm:text-xl leading-5 text-secondary",
                          weekDay === dayOfWeek && "font-bold"
                        )}
                      >
                        {formatMilitaryTime(daySchedule.timings[0])} -{" "}
                        {formatMilitaryTime(daySchedule.timings[1])}
                      </span>
                    );
                  })()}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
      <ClubDetail.CallToDownloadSection />
      <ClubDetail.ExploreOtherClubsSection clubs={otherClubs.docs} />
      <ClubDetail.FAQSection faqDetails={clubData?.faqs} />
    </section>
  );
}
