import { fetchData } from "@/client";
import {
  CitiesQuery,
  ClubCategoriesQuery,
  ClubsDocument,
  ClubsQuery,
  ClubsQueryVariables,
} from "@/generated/graphql";
import Image from "next/image";
import React from "react";

export const revalidate = 3600;

type ClubsProps = {
  clubs: ClubsQuery["clubs"];
  city: CitiesQuery["cities"]["docs"][number];
  categories: ClubCategoriesQuery["clubCategories"]["docs"];
};

export default async function BestClubsSection({
  clubs,
  city,
  categories,
}: ClubsProps) {
  const clubsData = clubs;
  const cityName = city.name;

  const res = await fetchData<ClubsQuery, ClubsQueryVariables>(ClubsDocument, {
    clubsInput: { city: city._id, featured: true },
  })();
  if (!res?.clubs) {
    return null;
  }

  const featuredClubs = res.clubs.totalDocs;
  const totalClubCatogories = categories.length;
  const featureClubCoverImage = clubsData.docs[0]?.images[0];
  const featuredClubDescription = clubsData.docs[0]?.description;
  const featuredClubName = clubsData.docs[0]?.name;
  return (
    <section className="flex flex-col bg-[#FFFBF3] sm:flex-row sm:items-center sm:justify-center sm:bg-background container sm:gap-[28px] xl:gap-[56px]">
      <div className="w-full h-[273px] sm:min-w-[280px] sm:h-[273px] lg:min-w-[350px] lg:h-[433px] xl:min-w-[604px] xl:h-[433px] rounded-[18px] mt-[42px] mb-6 xl:mt-[97px] xl:mb-[117px] relative overflow-hidden">
        <Image
          src={featureClubCoverImage || "/images/placeholderClubImage.png"}
          fill
          className="object-cover"
          alt="best-bars"
          loading="eager"
          sizes="(max-width: 768px) 100vw, 768px"
          quality={100}
          fetchPriority="high"
        />
      </div>
      <div className="flex flex-col w-full">
        <h2 className="text-2xl lg:text-3xl xl:text-[60px] leading-[37px] lg:leading-[67.2px] font-bold mt-6 sm:mt-0 text-text line-clamp-4">
          The Best Clubs In {cityName}
        </h2>
        <div className="text-sm lg:text-base xl:text-xl text-text mt-6 sm:mb-4 mb-11.5 xl:mb-12 lg:max-w-[90%] lg:leading-[32px] line-clamp-4">
          <span className="font-bold text-secondary text-base lg:text-xl xl:text-2xl">
            {featuredClubName}
          </span>
          <span className="ml-2">– {featuredClubDescription}</span>
        </div>
        <div className="flex flex-row flex-wrap gap-[43px] sm:gap-0 sm:w-full">
          <div className="flex flex-1 flex-col sm:items-center sm:justify-center gap-[19px] sm:gap-2 xl:gap-[19px] sm:border-t sm:border-inactive sm:pt-[20px] xl:pt-[39px] sm:border-r">
            <p className="text-text font-medium leading-[23px] text-base sm:text-sm xl:text-base">
              Total Clubs
            </p>
            <div className="text-muted-primary font-bold text-4xl sm:text-[22px] xl:text-[50px] leading-14">
              {clubsData.totalDocs === 0 ? "-" : clubsData.totalDocs}
            </div>
          </div>
          <div className="flex flex-1 flex-col sm:items-center gap-[19px] sm:gap-2 xl:gap-[19px] sm:border-t sm:border-inactive sm:pt-[20px] xl:pt-[39px] sm:border-r">
            <p className="text-text font-medium leading-[23px] text-base sm:text-sm xl:text-base">
              Total Categories
            </p>
            <div className="text-muted-primary font-bold text-4xl sm:text-[22px] xl:text-[50px] leading-14">
              {totalClubCatogories === 0 ? "-" : totalClubCatogories}
            </div>
          </div>
          <div className="flex flex-1 flex-col sm:items-center gap-[19px] sm:gap-2 xl:gap-[19px] sm:border-t sm:border-inactive sm:pt-[20px] xl:pt-[39px]">
            <p className="text-text font-medium leading-[23px] text-base sm:text-sm xl:text-base">
              Popular Clubs
            </p>
            <div className="text-muted-primary font-bold text-4xl sm:text-[22px] xl:text-[50px] leading-14">
              {featuredClubs === 0 ? "-" : featuredClubs}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
