import NotFound from "@/app/not-found";
import { fetchData } from "@/client";
import ClubsHomePage from "@/elements/clubs-homepage";
import {
  CitiesDocument,
  CitiesQuery,
  CitiesQueryVariables,
  ClubCategoriesDocument,
  ClubCategoriesQuery,
  ClubCategoriesQueryVariables,
  ClubCategoryByNameDocument,
  ClubCategoryByNameQuery,
  ClubCategoryByNameQueryVariables,
  ClubsDocument,
  ClubsQuery,
  ClubsQueryVariables,
} from "@/generated/graphql";
import { Metadata } from "next";
import { softwareApplicationLd } from "../bars/page";

export const revalidate = 3600;

export async function generateStaticParams() {
  const res = await fetchData<CitiesQuery, CitiesQueryVariables>(
    CitiesDocument,
    {}
  )();

  const cities = res?.cities?.docs || [];

  return cities.map((city) => ({
    city: city.slug,
    coverImage: city.coverImage,
  }));
}

export const generateMetadata = async ({
  params,
}: {
  params: Promise<{ city: string; coverImage: string }>;
}): Promise<Metadata> => {
  const { city: citySlug, coverImage: coverImageParam } = await params;

  const selectedCityRes = await fetchData<CitiesQuery, CitiesQueryVariables>(
    CitiesDocument,
    { citiesInput: { slug: citySlug } }
  )();

  const city = selectedCityRes?.cities?.docs[0];

  return {
    title: `Find the Best Clubs in ${city.name} | Techno, Hip-Hop, Rap, Latin & More
`,
    description: `Explore top nightclubs in ${city.name} by music style, from techno and rap to hip-hop and Latin parties. Discover opening hours, entry fees, photos, and plan your perfect night out.`,
    openGraph: {
      title: `Find the Best Clubs in ${city.name} | Techno, Hip-Hop, Rap, Latin & More
`,
      description: `Explore top nightclubs in ${city.name} by music style, from techno and rap to hip-hop and Latin parties. Discover opening hours, entry fees, photos, and plan your perfect night out.`,
      url: `https://seeker.com/${citySlug}/clubs`,
      images: [
        {
          url: coverImageParam,
          width: 1200,
          height: 630,
          alt: `Best clubs in ${city.name}`,
        },
      ],
    },
  };
};

export default async function Clubs({
  params,
  searchParams,
}: {
  params: Promise<{ city: string }>;
  searchParams: Promise<{ [key: string]: string | undefined }>;
}) {
  const { category, page } = await searchParams;
  const currentPage = Number(page) || 1;
  const { city: cityParam } = await params;
  try {
    // Fetch cities with error handling
    const citiesResponse = await fetchData<CitiesQuery, CitiesQueryVariables>(
      CitiesDocument,
      {
        citiesInput: {
          slug: cityParam,
        },
      }
    )();

    if (!citiesResponse?.cities?.docs?.[0]) {
      return (
        <NotFound
          error={new Error("We couldn't find the page you are looking for...")}
        />
      );
    }

    const city = citiesResponse.cities.docs[0];

    // Fetch club categories with error handling
    const clubCategoriesResponse = await fetchData<
      ClubCategoriesQuery,
      ClubCategoriesQueryVariables
    >(ClubCategoriesDocument)();

    if (!clubCategoriesResponse?.clubCategories) {
      return (
        <NotFound
          error={
            new Error(
              "Something went wrong while fetching the club categories..."
            )
          }
        />
      );
    }

    const { clubCategories } = clubCategoriesResponse;

    let categoryRes: ClubCategoryByNameQuery | undefined;

    if (category?.length) {
      const res = await fetchData<
        ClubCategoryByNameQuery,
        ClubCategoryByNameQueryVariables
      >(ClubCategoryByNameDocument, { slug: category })();
      categoryRes = res;
    }

    // Fetch clubs with proper error handling
    const clubsResponse = await fetchData<ClubsQuery, ClubsQueryVariables>(
      ClubsDocument,
      {
        clubsInput: {
          city: city._id,
          categories: categoryRes?.clubCategory?.id
            ? [categoryRes.clubCategory.id]
            : undefined,
        },
        paginationInput: { limit: 8, page: currentPage },
      }
    )();

    if (!clubsResponse?.clubs) {
      return <NotFound error={new Error("No clubs found")} />;
    }

    const { clubs: clubsWithFilter } = clubsResponse;

    if (clubsWithFilter.docs === undefined) {
      return <NotFound error={new Error("No clubs found")} />;
    }

    return (
      <>
        <script
          key="software-jsonld"
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(softwareApplicationLd).replace(
              /</g,
              "\\u003c"
            ),
          }}
        />
        <ClubsHomePage.HeroSection
          category={category}
          description={categoryRes?.clubCategory?.description}
        />

        <ClubsHomePage.BestClubsSection
          categories={clubCategories?.docs}
          clubs={clubsWithFilter}
          city={city}
        />
        <ClubsHomePage.ClubsSection cityId={city.id} />
        <ClubsHomePage.CallToDownloadSection />
      </>
    );
  } catch (error) {
    <NotFound error={error as Error} />;
  }
}
