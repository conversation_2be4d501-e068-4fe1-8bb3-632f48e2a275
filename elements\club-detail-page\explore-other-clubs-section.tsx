import ClubCard from "@/components/cards/club.card";
import HorizontalScrollCards from "@/components/common/HorizontalScrollCards";
import { ClubsQuery } from "@/generated/graphql";
import { cn } from "@/lib/utils";
import Link from "next/link";

export const revalidate = 3600;

type ExploreOtherClubsSectionProps = {
  clubs: ClubsQuery["clubs"]["docs"];
};
export default function ExploreOtherClubsSection({
  clubs,
}: ExploreOtherClubsSectionProps) {
  const clubsData = clubs;
  const showClubs = clubsData?.filter((club) => club.featured).length === 0;
  return (
    <section
      className={cn(
        "w-full bg-background flex flex-col items-center container md:my-[60px] my-[43px]",
        showClubs && "hidden"
      )}
    >
      <h2 className="font-bold text-xl mb-[48px] md:mb-[40px] md:text-4xl lg:text-[40px]">
        Explore Other Clubs
      </h2>
      <div className="flex flex-col gap-[47px] sm:gap-0 w-full">
        <HorizontalScrollCards>
          {clubsData
            ?.filter((club) => club.featured)
            .map((card) => (
              <Link
                href={`/${card.city.slug}/clubs/${card.slug}`}
                key={card._id}
              >
                <ClubCard data={card} wrapperClassName="w-[298px]" />
              </Link>
            ))}
        </HorizontalScrollCards>
      </div>
    </section>
  );
}
