import { IconProps } from "@/types";
import * as React from "react";
const PlusIcon = ({ className, color, height, width }: IconProps) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 14 14"
    fill="none"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M6.0668 6.0668V0.466797H7.93346V6.0668H13.5335V7.93346H7.93346V13.5335H6.0668V7.93346H0.466797V6.0668H6.0668Z"
      fill={color}
    />
  </svg>
);
export default PlusIcon;
