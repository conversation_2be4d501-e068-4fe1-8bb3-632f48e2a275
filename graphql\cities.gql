query Cities($citiesInput: CitiesInput, $paginationInput: PaginationInput) {
  cities(citiesInput: $citiesInput, paginationInput: $paginationInput) {
    totalDocs
    limit
    page
    totalPages
    prevPage
    nextPage
    hasPrevPage
    hasNextPage
    pagingCounter
    docs {
      _id
      id
      createdAt
      updatedAt
      name
      image
      coverImage
      heading
      subHeading
      slug
    }
  }
}

query CityStats($cityId: String!) {
  bars(barsInput: { city: $cityId }) {
    totalDocs
  }
  clubs(clubsInput: { city: $cityId }) {
    totalDocs
  }
}
query City($cityId: ID!) {
  city(id: $cityId) {
    _id
    name
    subHeading
    coverImage
    createdAt
    heading
    id
    image
    updatedAt
    slug
  }
}
