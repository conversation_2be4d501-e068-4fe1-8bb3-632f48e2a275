import { IconProps } from "@/types";
import * as React from "react";
const PinSolidIcon = ({ className, color, height, width }: IconProps) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 14 19"
    fill="none"
    className={className}
  >
    <path
      d="M6.475 17.8984C5.9125 17.0781 5.26797 16.1523 4.54141 15.1211C3.83828 14.0898 3.33437 13.3633 3.02969 12.9414C2.725 12.5195 2.37344 12.0039 1.975 11.3945C1.57656 10.7617 1.30703 10.3164 1.16641 10.0586C1.04922 9.80078 0.908594 9.47266 0.744531 9.07422C0.603906 8.67578 0.510156 8.33594 0.463281 8.05469C0.439844 7.75 0.428125 7.39844 0.428125 7C0.428125 5.125 1.08437 3.53125 2.39687 2.21875C3.70937 0.90625 5.30312 0.25 7.17812 0.25C9.05312 0.25 10.6469 0.90625 11.9594 2.21875C13.2719 3.53125 13.9281 5.125 13.9281 7C13.9281 7.39844 13.9047 7.75 13.8578 8.05469C13.8344 8.33594 13.7406 8.67578 13.5766 9.07422C13.4359 9.47266 13.2953 9.80078 13.1547 10.0586C13.0375 10.3164 12.7797 10.7617 12.3812 11.3945C11.9828 12.0039 11.6312 12.5195 11.3266 12.9414C11.0219 13.3633 10.5062 14.0898 9.77969 15.1211C9.07656 16.1523 8.44375 17.0781 7.88125 17.8984C7.71719 18.1328 7.48281 18.25 7.17812 18.25C6.87344 18.25 6.63906 18.1328 6.475 17.8984ZM5.17422 9.00391C5.73672 9.54297 6.40469 9.8125 7.17812 9.8125C7.95156 9.8125 8.60781 9.54297 9.14687 9.00391C9.70937 8.44141 9.99062 7.77344 9.99062 7C9.99062 6.22656 9.70937 5.57031 9.14687 5.03125C8.60781 4.46875 7.95156 4.1875 7.17812 4.1875C6.40469 4.1875 5.73672 4.46875 5.17422 5.03125C4.63516 5.57031 4.36562 6.22656 4.36562 7C4.36562 7.77344 4.63516 8.44141 5.17422 9.00391Z"
      fill={color}
    />
  </svg>
);
export default PinSolidIcon;
