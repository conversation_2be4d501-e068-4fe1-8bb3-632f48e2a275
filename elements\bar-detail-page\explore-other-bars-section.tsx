import BarCard from "@/components/cards/bar.card";
import HorizontalScrollCards from "@/components/common/HorizontalScrollCards";
import { BarsQuery } from "@/generated/graphql";
import { cn } from "@/lib/utils";
import Link from "next/link";

import React from "react";
type ExploreOtherBarsSectionProps = {
  bars: BarsQuery["bars"]["docs"];
};
export default function ExploreOtherBarsSection({
  bars,
}: ExploreOtherBarsSectionProps) {
  const barsData = bars;
  const showBars = barsData?.length === 0;

  return (
    <section
      className={cn(
        "w-full flex flex-col items-center container md:pt-[102px] pt-[43px] md:pb-[95px] pb-[38px] scroll-mt-[160px]",
        showBars && "hidden"
      )}
    >
      <h2 className="font-bold text-xl mb-3 lg:mb-[20px]  md:text-4xl lg:text-[40px]">
        Explore Popular Bars
      </h2>
      <div className="flex flex-col gap-[47px] sm:gap-0 w-full">
        <HorizontalScrollCards>
          {barsData.map((card) => (
            <Link href={`/${card.city.slug}/bars/${card.slug}`} key={card._id}>
              <BarCard data={card} wrapperClassName="w-[298px]" />
            </Link>
          ))}
        </HorizontalScrollCards>
      </div>
    </section>
  );
}
