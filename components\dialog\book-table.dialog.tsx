import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

export default function BookTableDialog({
  place,
}: {
  place: string | undefined;
}) {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <div className="px-10 btn-gradient-4 sm:px-[28px] py-[13px] mb-[33px] lg:text-xl font-bold font-montserrat leading-5 text-white rounded-full cursor-pointer transition-all hover:scale-101 duration-300">
          BOOK A TABLE
        </div>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            Book a Table in{" "}
            <span className="font-bold text-primary">{place}</span>
          </DialogTitle>
          <DialogDescription className="self-center mt-4 bg-footerbg px-4 py-1 rounded-sm font-semibold">
            WIP
          </DialogDescription>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  );
}
