import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import { fetchData } from '@/client';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  DateTime: { input: Date; output: Date; }
};

export type Address = {
  __typename?: 'Address';
  /** Address */
  address: Scalars['String']['output'];
  /** Location */
  location: Location;
  /** Metro line */
  metroLine?: Maybe<Scalars['String']['output']>;
  /** Metro station */
  metroStation?: Maybe<Scalars['String']['output']>;
};

export type AddressInput = {
  /** Address */
  address: Scalars['String']['input'];
  /** Location */
  location: LocationInput;
  /** Metro line */
  metroLine?: InputMaybe<Scalars['String']['input']>;
  /** Metro station */
  metroStation?: InputMaybe<Scalars['String']['input']>;
};

export type Bar = {
  __typename?: 'Bar';
  /** MongoDB ObjectId */
  _id: Scalars['ID']['output'];
  /** Bar address */
  address?: Maybe<Address>;
  /** Opening hours */
  businessHours?: Maybe<BarBusinessHoursSchedule>;
  categories: Array<BarCategory>;
  city: City;
  /** Bar phone number */
  contact?: Maybe<Contact>;
  /** Bar cover image URL */
  coverImage?: Maybe<Scalars['String']['output']>;
  /** Document creation timestamp */
  createdAt: Scalars['DateTime']['output'];
  /** Bar description */
  description: Scalars['String']['output'];
  distanceInMeters?: Maybe<Scalars['Float']['output']>;
  /** Frequently Asked Questions */
  faqs?: Maybe<Array<Faq>>;
  /** Whether bar is featured */
  featured: Scalars['Boolean']['output'];
  /** Happy hours schedule */
  happyHours?: Maybe<BarHappyHoursSchedule>;
  id: Scalars['ID']['output'];
  /** Array of bar image URLs */
  images: Array<Scalars['String']['output']>;
  language?: Maybe<LanguageIsoCode>;
  /** Bar logo URL */
  logo?: Maybe<Scalars['String']['output']>;
  /** Bar menu */
  menu?: Maybe<BarMenu>;
  /** Bar name */
  name: Scalars['String']['output'];
  neighborhood?: Maybe<Neighborhood>;
  /** Bar rating (0-5) */
  rating: Scalars['Float']['output'];
  /** SEO fields for search engine optimization and social media sharing */
  seo?: Maybe<Seo>;
  /** SEO-friendly URL slug for the bar */
  slug: Scalars['String']['output'];
  /** Bar status */
  status: BarStatus;
  tags: Array<Tag>;
  /** Document last update timestamp */
  updatedAt: Scalars['DateTime']['output'];
};

export type BarBusinessHoursInput = {
  /** Array of day timings. Each day can have opening time only or both opening and closing times in 24hr HHMM format */
  schedule: Array<DayTimingInput>;
};

export type BarBusinessHoursSchedule = {
  __typename?: 'BarBusinessHoursSchedule';
  /** Array of day timings. Each day can have opening time only or both opening and closing times in 24hr HHMM format */
  schedule: Array<BarDayTiming>;
};

export type BarCategoriesInput = {
  /** Pagination options */
  pagination?: InputMaybe<PaginationInput>;
  /** Filter by slug */
  slug?: InputMaybe<Scalars['String']['input']>;
};

export type BarCategory = {
  __typename?: 'BarCategory';
  /** MongoDB ObjectId */
  _id: Scalars['ID']['output'];
  /** Document creation timestamp */
  createdAt: Scalars['DateTime']['output'];
  /** Bar Category description */
  description?: Maybe<Scalars['String']['output']>;
  /** Bar Category Icon (support lucide-react) */
  icon?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  /** Bar Category image */
  image?: Maybe<Scalars['String']['output']>;
  /** Bar Category name */
  name: Scalars['String']['output'];
  /** SEO-friendly URL slug for the bar category */
  slug: Scalars['String']['output'];
  /** Document last update timestamp */
  updatedAt: Scalars['DateTime']['output'];
};

export type BarDayTiming = {
  __typename?: 'BarDayTiming';
  crossingMidnight: Scalars['Boolean']['output'];
  /** Day of the week */
  day: DayOfWeek;
  timings: Array<Scalars['Float']['output']>;
};

export type BarHappyHoursInput = {
  /** Array of happy hour day timings. Each day can have start time only or both start and end times in 24hr HHMM format */
  schedule: Array<DayTimingInput>;
};

export type BarHappyHoursSchedule = {
  __typename?: 'BarHappyHoursSchedule';
  /** Array of happy hour day timings. Each day can have start time only or both start and end times in 24hr HHMM format */
  schedule: Array<BarDayTiming>;
};

export type BarMenu = {
  __typename?: 'BarMenu';
  /** Menu currency (e.g., USD, EUR, INR) */
  currency: Scalars['String']['output'];
  happyHourStartingPrice?: Maybe<Scalars['Float']['output']>;
  /** Array of menu sections */
  sections: Array<BarMenuSection>;
  startingPrice?: Maybe<Scalars['Float']['output']>;
};

export type BarMenuInput = {
  /** Menu currency (e.g., USD, EUR, INR) */
  currency: Scalars['String']['input'];
  happyHourStartingPrice?: InputMaybe<Scalars['Float']['input']>;
  /** Array of menu sections */
  sections: Array<BarMenuSectionInput>;
  startingPrice?: InputMaybe<Scalars['Float']['input']>;
};

export type BarMenuItem = {
  __typename?: 'BarMenuItem';
  /** Whether the item is available */
  available: BarMenuItemAvailability;
  /** Optional happy hour price */
  happyHourPrice?: Maybe<Scalars['Float']['output']>;
  /** Menu item name */
  name: Scalars['String']['output'];
  /** Menu item price */
  price: Scalars['Float']['output'];
};

export enum BarMenuItemAvailability {
  Available = 'AVAILABLE',
  Unavailable = 'UNAVAILABLE'
}

export type BarMenuItemInput = {
  /** Whether the item is available */
  available: BarMenuItemAvailability;
  /** Optional happy hour price */
  happyHourPrice?: InputMaybe<Scalars['Float']['input']>;
  /** Menu item name */
  name: Scalars['String']['input'];
  /** Menu item price */
  price: Scalars['Float']['input'];
};

export type BarMenuSection = {
  __typename?: 'BarMenuSection';
  /** Array of menu items in this section */
  items: Array<BarMenuItem>;
  /** Section name (e.g., "Bières (pression)50cl", "Pizza", "Plats") */
  name: Scalars['String']['output'];
};

export type BarMenuSectionInput = {
  /** Array of menu items in this section */
  items: Array<BarMenuItemInput>;
  /** Section name (e.g., "Bières (pression)50cl", "Pizza", "Plats") */
  name: Scalars['String']['input'];
};

export enum BarStatus {
  Active = 'ACTIVE',
  Inactive = 'INACTIVE',
  Pending = 'PENDING',
  Suspended = 'SUSPENDED'
}

export type BarsInput = {
  /** Optional Day range filter for business hours */
  businessHours?: InputMaybe<DayRangeFilter>;
  /** Filter bars with business hours ending after a specific time (HHMM format) */
  businessHoursEndingAfter?: InputMaybe<Scalars['Float']['input']>;
  /** Filter bars with business hours ending before a specific time (HHMM format) */
  businessHoursEndingBefore?: InputMaybe<Scalars['Float']['input']>;
  /** Filter bars with business hours ending within a specific time range */
  businessHoursEndingBetween?: InputMaybe<TimeRangeFilter>;
  /** Filter by category IDs */
  categories?: InputMaybe<Array<Scalars['String']['input']>>;
  /** Filter by city ID */
  city?: InputMaybe<Scalars['String']['input']>;
  /** Filter bars that are currently in business hours */
  currentlyInBusinessHours?: InputMaybe<Scalars['Boolean']['input']>;
  /** Filter bars that are currently in happy hours */
  currentlyInHappyHours?: InputMaybe<Scalars['Boolean']['input']>;
  /** Filter by featured status */
  featured?: InputMaybe<Scalars['Boolean']['input']>;
  /** Filter bars within a specific happy hour starting price range */
  happyHourStartingPriceRange?: InputMaybe<PriceRangeInput>;
  /** Optional Day range filter for happy hours */
  happyHours?: InputMaybe<DayRangeFilter>;
  /** Filter bars with happy hours ending after a specific time (HHMM format) */
  happyHoursEndingAfter?: InputMaybe<Scalars['Float']['input']>;
  /** Filter bars with happy hours ending before a specific time (HHMM format) */
  happyHoursEndingBefore?: InputMaybe<Scalars['Float']['input']>;
  /** Filter bars with happy hours ending within a specific time range */
  happyHoursEndingBetween?: InputMaybe<TimeRangeFilter>;
  /** Filter by maximum rating */
  maxRating?: InputMaybe<Scalars['Float']['input']>;
  /** Filter by minimum rating */
  minRating?: InputMaybe<Scalars['Float']['input']>;
  /** Filter by distance - find bars within specified distance sorted by nearness */
  near?: InputMaybe<NearQueryInput>;
  /** Filter by neighborhood ID */
  neighborhood?: InputMaybe<Scalars['String']['input']>;
  /** Filter by bar slug */
  slug?: InputMaybe<Scalars['String']['input']>;
  /** Filter bars within a specific starting price range */
  startingPriceRange?: InputMaybe<PriceRangeInput>;
  /** Filter by bar status */
  status?: InputMaybe<BarStatus>;
  /** Filter by tag IDs */
  tags?: InputMaybe<Array<Scalars['String']['input']>>;
  /** Filter by radius - find bars within specified radius */
  withinRadius?: InputMaybe<RadiusQueryInput>;
};

export type Block = GlobalBlock | LocalBlock;

export enum BlockActiveStatus {
  Active = 'ACTIVE',
  Inactive = 'INACTIVE'
}

export enum BlockLocationScope {
  Global = 'GLOBAL',
  Local = 'LOCAL'
}

export type BlockSequenceInput = {
  /** Block ID */
  blockId: Scalars['String']['input'];
  /** New sequence number */
  sequence: Scalars['Float']['input'];
};

export enum BlockType {
  Bar = 'BAR',
  Club = 'CLUB'
}

/** Business hour filtering scenarios for bars */
export enum BusinessHourScenario {
  Current = 'CURRENT',
  EndingEarlyMorning = 'ENDING_EARLY_MORNING',
  EndingLateNight = 'ENDING_LATE_NIGHT'
}

export type BusinessHoursInput = {
  /** Array of day timings. Each day can have opening time only or both opening and closing times in 24hr HHMM format */
  schedule: Array<DayTimingInput>;
};

export type BusinessHoursSchedule = {
  __typename?: 'BusinessHoursSchedule';
  /** Array of day timings. Each day can have opening time only or both opening and closing times in 24hr HHMM format */
  schedule: Array<DayTiming>;
};

export type CitiesInput = {
  /** Filter by city slug */
  slug?: InputMaybe<Scalars['String']['input']>;
};

export type City = {
  __typename?: 'City';
  /** MongoDB ObjectId */
  _id: Scalars['ID']['output'];
  /** Cover image for the city */
  coverImage?: Maybe<Scalars['String']['output']>;
  /** Document creation timestamp */
  createdAt: Scalars['DateTime']['output'];
  /** Main heading for the city */
  heading: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  /** Main image of the city */
  image: Scalars['String']['output'];
  /** Geographical location of the city */
  location: Location;
  /** Name of the city */
  name: Scalars['String']['output'];
  /** SEO-friendly URL slug for the city */
  slug: Scalars['String']['output'];
  /** Sub heading for the city */
  subHeading?: Maybe<Scalars['String']['output']>;
  /** Document last update timestamp */
  updatedAt: Scalars['DateTime']['output'];
};

export type Club = {
  __typename?: 'Club';
  /** MongoDB ObjectId */
  _id: Scalars['ID']['output'];
  /** Club address */
  address?: Maybe<Address>;
  /** Opening hours */
  businessHours?: Maybe<BusinessHoursSchedule>;
  categories: Array<ClubCategory>;
  city: City;
  /** Club phone number */
  contact?: Maybe<Contact>;
  /** Club cover image URL */
  coverImage?: Maybe<Scalars['String']['output']>;
  /** Document creation timestamp */
  createdAt: Scalars['DateTime']['output'];
  /** Club description */
  description: Scalars['String']['output'];
  distanceInMeters?: Maybe<Scalars['Float']['output']>;
  /** Frequently Asked Questions */
  faqs?: Maybe<Array<Faq>>;
  /** Whether club is featured */
  featured: Scalars['Boolean']['output'];
  id: Scalars['ID']['output'];
  /** Array of club image URLs */
  images: Array<Scalars['String']['output']>;
  language?: Maybe<LanguageIsoCode>;
  /** Club logo URL */
  logo?: Maybe<Scalars['String']['output']>;
  /** Club menu */
  menu?: Maybe<Menu>;
  /** Club name */
  name: Scalars['String']['output'];
  neighborhood?: Maybe<Neighborhood>;
  /** Club rating (0-5) */
  rating: Scalars['Float']['output'];
  /** SEO fields for search engine optimization and social media sharing */
  seo?: Maybe<Seo>;
  /** SEO-friendly URL slug for the club */
  slug: Scalars['String']['output'];
  /** Club status */
  status: ClubStatus;
  tags: Array<Tag>;
  /** Document last update timestamp */
  updatedAt: Scalars['DateTime']['output'];
};

/** Business hour filtering scenarios for clubs */
export enum ClubBusinessHourScenario {
  Current = 'CURRENT',
  EndingEarlyMorning = 'ENDING_EARLY_MORNING',
  EndingLateNight = 'ENDING_LATE_NIGHT'
}

export type ClubCategoriesInput = {
  /** Pagination options */
  pagination?: InputMaybe<PaginationInput>;
  /** Filter by slug */
  slug?: InputMaybe<Scalars['String']['input']>;
};

export type ClubCategory = {
  __typename?: 'ClubCategory';
  /** MongoDB ObjectId */
  _id: Scalars['ID']['output'];
  /** Document creation timestamp */
  createdAt: Scalars['DateTime']['output'];
  /** Club Category description */
  description?: Maybe<Scalars['String']['output']>;
  /** Club Category Icon (support lucide-react) */
  icon?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  /** Club Category image */
  image?: Maybe<Scalars['String']['output']>;
  /** Club Category name */
  name: Scalars['String']['output'];
  /** SEO-friendly URL slug for the club category */
  slug: Scalars['String']['output'];
  /** Document last update timestamp */
  updatedAt: Scalars['DateTime']['output'];
};

export enum ClubStatus {
  Active = 'ACTIVE',
  Inactive = 'INACTIVE',
  Pending = 'PENDING',
  Suspended = 'SUSPENDED'
}

export type ClubsInput = {
  /** Optional Day range filter for business hours */
  businessHours?: InputMaybe<DayRangeFilter>;
  /** Filter clubs with business hours ending after a specific time (HHMM format) */
  businessHoursEndingAfter?: InputMaybe<Scalars['Float']['input']>;
  /** Filter clubs with business hours ending before a specific time (HHMM format) */
  businessHoursEndingBefore?: InputMaybe<Scalars['Float']['input']>;
  /** Filter clubs with business hours ending within a specific time range */
  businessHoursEndingBetween?: InputMaybe<TimeRangeFilter>;
  /** Filter by category IDs */
  categories?: InputMaybe<Array<Scalars['String']['input']>>;
  /** Filter by city ID */
  city?: InputMaybe<Scalars['String']['input']>;
  /** Bar contact information (phone) */
  contact?: InputMaybe<ContactInput>;
  /** Filter clubs that are currently in business hours */
  currentlyInBusinessHours?: InputMaybe<Scalars['Boolean']['input']>;
  /** Filter by featured status */
  featured?: InputMaybe<Scalars['Boolean']['input']>;
  /** Filter by maximum rating */
  maxRating?: InputMaybe<Scalars['Float']['input']>;
  /** Filter by minimum rating */
  minRating?: InputMaybe<Scalars['Float']['input']>;
  /** Filter by distance - find clubs within specified distance sorted by nearness */
  near?: InputMaybe<NearQueryInput>;
  /** Filter by neighborhood ID */
  neighborhood?: InputMaybe<Scalars['String']['input']>;
  /** Filter by club slug */
  slug?: InputMaybe<Scalars['String']['input']>;
  /** Filter clubs within a specific starting price range */
  startingPriceRange?: InputMaybe<PriceRangeInput>;
  /** Filter by club status */
  status?: InputMaybe<ClubStatus>;
  /** Filter by tag IDs */
  tags?: InputMaybe<Array<Scalars['String']['input']>>;
  /** Filter by radius - find clubs within specified radius */
  withinRadius?: InputMaybe<RadiusQueryInput>;
};

/** Contact */
export type Contact = {
  __typename?: 'Contact';
  countryCode: Scalars['String']['output'];
  phone: Scalars['String']['output'];
};

export type ContactInput = {
  countryCode: Scalars['String']['input'];
  phone: Scalars['String']['input'];
};

export type CreateBarCategoryInput = {
  /** Bar Category description */
  description?: InputMaybe<Scalars['String']['input']>;
  /** Bar Category Icon (support lucide-react) */
  icon?: InputMaybe<Scalars['String']['input']>;
  /** Bar Category image */
  image?: InputMaybe<Scalars['String']['input']>;
  /** Bar Category name */
  name: Scalars['String']['input'];
  /** SEO-friendly URL slug for the bar category */
  slug: Scalars['String']['input'];
};

export type CreateBarInput = {
  /** Bar address */
  address?: InputMaybe<AddressInput>;
  /** Opening hours */
  businessHours?: InputMaybe<BarBusinessHoursInput>;
  /** Bar category IDs */
  categories: Array<Scalars['String']['input']>;
  /** Bar city IDs */
  city: Scalars['String']['input'];
  /** Bar contact information (phone) */
  contact?: InputMaybe<ContactInput>;
  /** Bar cover image URL */
  coverImage?: InputMaybe<Scalars['String']['input']>;
  /** Bar description */
  description: Scalars['String']['input'];
  /** Frequently Asked Questions */
  faqs?: InputMaybe<Array<FaqInput>>;
  /** Whether bar is featured */
  featured: Scalars['Boolean']['input'];
  /** Happy hours schedule */
  happyHours?: InputMaybe<BarHappyHoursInput>;
  /** Array of bar image URLs */
  images: Array<Scalars['String']['input']>;
  language?: InputMaybe<LanguageIsoCode>;
  /** Bar logo URL */
  logo?: InputMaybe<Scalars['String']['input']>;
  /** Bar menu */
  menu?: InputMaybe<BarMenuInput>;
  /** Bar name */
  name: Scalars['String']['input'];
  /** Optional neighborhood ID */
  neighborhood?: InputMaybe<Scalars['String']['input']>;
  /** Bar rating (0-5) */
  rating: Scalars['Float']['input'];
  /** SEO fields for search engine optimization and social media sharing */
  seo?: InputMaybe<SeoInput>;
  /** SEO-friendly URL slug for the bar */
  slug: Scalars['String']['input'];
  /** Bar status */
  status: BarStatus;
  /** Bar tag IDs */
  tags?: InputMaybe<Array<Scalars['String']['input']>>;
};

export type CreateCityInput = {
  /** Cover image for the city */
  coverImage?: InputMaybe<Scalars['String']['input']>;
  /** Main heading for the city */
  heading: Scalars['String']['input'];
  /** Main image of the city */
  image: Scalars['String']['input'];
  /** Geographical location of the city */
  location: LocationInput;
  /** Name of the city */
  name: Scalars['String']['input'];
  /** SEO-friendly URL slug for the city */
  slug: Scalars['String']['input'];
  /** Sub heading for the city */
  subHeading?: InputMaybe<Scalars['String']['input']>;
};

export type CreateClubCategoryInput = {
  /** Club Category description */
  description?: InputMaybe<Scalars['String']['input']>;
  /** Club Category Icon (support lucide-react) */
  icon?: InputMaybe<Scalars['String']['input']>;
  /** Club Category image */
  image?: InputMaybe<Scalars['String']['input']>;
  /** Club Category name */
  name: Scalars['String']['input'];
  /** SEO-friendly URL slug for the club category */
  slug: Scalars['String']['input'];
};

export type CreateClubInput = {
  /** Club address */
  address?: InputMaybe<AddressInput>;
  /** Opening hours */
  businessHours?: InputMaybe<BusinessHoursInput>;
  /** Club category IDs */
  categories: Array<Scalars['String']['input']>;
  /** Club city IDs */
  city: Scalars['String']['input'];
  /** Club contact information (phone) */
  contact?: InputMaybe<ContactInput>;
  /** Club cover image URL */
  coverImage?: InputMaybe<Scalars['String']['input']>;
  /** Club description */
  description: Scalars['String']['input'];
  /** Frequently Asked Questions */
  faqs?: InputMaybe<Array<FaqInput>>;
  /** Whether club is featured */
  featured: Scalars['Boolean']['input'];
  /** Array of club image URLs */
  images: Array<Scalars['String']['input']>;
  /** Club logo URL */
  logo?: InputMaybe<Scalars['String']['input']>;
  /** Club menu */
  menu?: InputMaybe<MenuInput>;
  /** Club name */
  name: Scalars['String']['input'];
  /** Optional neighborhood ID */
  neighborhood?: InputMaybe<Scalars['String']['input']>;
  /** Club rating (0-5) */
  rating: Scalars['Float']['input'];
  /** SEO fields for search engine optimization and social media sharing */
  seo?: InputMaybe<SeoInput>;
  /** SEO-friendly URL slug for the club */
  slug: Scalars['String']['input'];
  /** Club status */
  status: ClubStatus;
  /** Club tag IDs */
  tags?: InputMaybe<Array<Scalars['String']['input']>>;
};

export type CreateLayoutBlockInput = {
  /** Active status of the block */
  active: BlockActiveStatus;
  /** Block data - can be either GlobalBlock or LocalBlock */
  block: GlobalOrLocalInput;
  blockType: BlockType;
  /** GraphQL filter string */
  filter?: InputMaybe<Scalars['String']['input']>;
  /** Layout ID this block belongs to */
  layoutId: Scalars['String']['input'];
  /** Name of the block */
  name: Scalars['String']['input'];
};

export type CreateLayoutInput = {
  /** Active status of the layout */
  active: LayoutActiveStatus;
  /** Name of the layout */
  name: Scalars['String']['input'];
  /** Unique scope */
  scope: Scalars['String']['input'];
};

export type CreateNeighborhoodInput = {
  /** City ID this neighborhood belongs to */
  city: Scalars['String']['input'];
  /** Main cover image of the neighborhood */
  coverImage?: InputMaybe<Scalars['String']['input']>;
  /** Array of neighborhood image URLs */
  images?: InputMaybe<Array<Scalars['String']['input']>>;
  /** Geographical location of the neighborhood */
  location: LocationInput;
  /** Name of the neighborhood */
  name: Scalars['String']['input'];
  /** SEO-friendly URL slug for the neighborhood */
  slug: Scalars['String']['input'];
};

export type CreateTagInput = {
  /** Tag description */
  description?: InputMaybe<Scalars['String']['input']>;
  /** Tag Icon (support lucide-react) */
  icon?: InputMaybe<Scalars['String']['input']>;
  /** Tag image */
  image?: InputMaybe<Scalars['String']['input']>;
  /** Tag name */
  name: Scalars['String']['input'];
};

export enum DayOfWeek {
  Friday = 'FRIDAY',
  Monday = 'MONDAY',
  Saturday = 'SATURDAY',
  Sunday = 'SUNDAY',
  Thursday = 'THURSDAY',
  Tuesday = 'TUESDAY',
  Wednesday = 'WEDNESDAY'
}

export type DayRangeFilter = {
  day: DayOfWeek;
  fromTime: Scalars['Float']['input'];
  toTime: Scalars['Float']['input'];
};

export type DayTiming = {
  __typename?: 'DayTiming';
  /** Whether the closing time is past midnight (endTime < startTime) */
  crossingMidnight: Scalars['Boolean']['output'];
  /** Day of the week */
  day: DayOfWeek;
  timings: Array<Scalars['Float']['output']>;
};

export type DayTimingInput = {
  /** Day of the week */
  day: DayOfWeek;
  /** Opening hours in 24hr format [openTime, closeTime]. Single number for opening time only, two numbers for open and close times. Times are in HHMM format (e.g., 900 for 9:00, 1730 for 17:30). Supports formats that can be converted by time.utils: 900, 1730, etc. */
  timings: Array<Scalars['Float']['input']>;
};

/** FAQ item with title and content */
export type Faq = {
  __typename?: 'FAQ';
  /** FAQ content/answer */
  content: Scalars['String']['output'];
  /** FAQ title/question */
  title: Scalars['String']['output'];
};

/** Input type for FAQ items */
export type FaqInput = {
  /** FAQ content/answer */
  content: Scalars['String']['input'];
  /** FAQ title/question */
  title: Scalars['String']['input'];
};

export type GlobalBlock = {
  __typename?: 'GlobalBlock';
  /** Location scope of the block */
  locationScope: BlockLocationScope;
};

export type GlobalOrLocalInput = {
  /** Cities */
  cities?: InputMaybe<Array<Scalars['String']['input']>>;
  /** Distance in meters for the block to be visible for the user */
  distanceMeters?: InputMaybe<Scalars['Float']['input']>;
  /** Location of the block */
  location?: InputMaybe<LocationInput>;
  /** Location scope of the block */
  locationScope: BlockLocationScope;
  /** Neighborhoods */
  neighborhoods?: InputMaybe<Array<Scalars['String']['input']>>;
};

/** Happy hour filtering scenarios */
export enum HappyHourScenario {
  Current = 'CURRENT',
  EndingEarlyMorning = 'ENDING_EARLY_MORNING',
  EndingLateNight = 'ENDING_LATE_NIGHT'
}

export enum LanguageIsoCode {
  En = 'EN',
  Fr = 'FR'
}

/** Language Input */
export type LanguageInput = {
  /** Language ISO code */
  language?: InputMaybe<LanguageIsoCode>;
};

export type Layout = {
  __typename?: 'Layout';
  /** MongoDB ObjectId */
  _id: Scalars['ID']['output'];
  /** Active status of the layout */
  active: LayoutActiveStatus;
  blocks: Array<LayoutBlock>;
  /** Document creation timestamp */
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  /** Name of the layout */
  name: Scalars['String']['output'];
  /** unique scope */
  scope: Scalars['String']['output'];
  /** Document last update timestamp */
  updatedAt: Scalars['DateTime']['output'];
};

export enum LayoutActiveStatus {
  Active = 'ACTIVE',
  Inactive = 'INACTIVE'
}

export type LayoutBlock = {
  __typename?: 'LayoutBlock';
  /** MongoDB ObjectId */
  _id: Scalars['ID']['output'];
  /** Active status of the block */
  active: BlockActiveStatus;
  /** Block data - can be either GlobalBlock or LocalBlock */
  block: Block;
  blockType: BlockType;
  /** Document creation timestamp */
  createdAt: Scalars['DateTime']['output'];
  /** GraphQL filter string */
  filter?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  /** Layout Id */
  layout: Layout;
  /** Name of the block */
  name: Scalars['String']['output'];
  /** Sequence of the block within the layout */
  sequence: Scalars['Float']['output'];
  /** Document last update timestamp */
  updatedAt: Scalars['DateTime']['output'];
};

export type LayoutBlocksInput = {
  /** Filter by block active status */
  active?: InputMaybe<BlockActiveStatus>;
  /** Filter by cities (searches within block cities) */
  cities?: InputMaybe<Array<Scalars['String']['input']>>;
  /** Filter by city ID (searches within block cities) */
  city?: InputMaybe<Scalars['String']['input']>;
  /** Filter by layout ID */
  layoutId?: InputMaybe<Scalars['String']['input']>;
  /** Filter by location scope */
  locationScope?: InputMaybe<BlockLocationScope>;
  /** Filter by block name (case-insensitive partial match) */
  name?: InputMaybe<Scalars['String']['input']>;
  /** Filter by distance - find blocks within specified distance sorted by nearness */
  near?: InputMaybe<NearQueryInput>;
  /** Filter by neighborhood ID (searches within block neighborhoods) */
  neighborhood?: InputMaybe<Scalars['String']['input']>;
  /** Filter by neighborhoods (searches within block neighborhoods) */
  neighborhoods?: InputMaybe<Array<Scalars['String']['input']>>;
  /** Filter by sequence number */
  sequence?: InputMaybe<Scalars['Float']['input']>;
  /** Filter by radius - find blocks within specified radius */
  withinRadius?: InputMaybe<RadiusQueryInput>;
};

export type LayoutsInput = {
  /** Filter by layout active status */
  active?: InputMaybe<LayoutActiveStatus>;
  /** Filter by layout scope */
  scope?: InputMaybe<Scalars['String']['input']>;
};

export type LocalBlock = {
  __typename?: 'LocalBlock';
  /** Cities */
  cities: Array<Scalars['String']['output']>;
  /** Distance in meters for the block to be visible for the user */
  distanceMeters: Scalars['Float']['output'];
  /** Location of the block */
  location: Location;
  /** Location scope of the block */
  locationScope: BlockLocationScope;
  /** Neighborhoods */
  neighborhoods: Array<Scalars['String']['output']>;
};

export type Location = {
  __typename?: 'Location';
  /** Center coordinates as [longitude, latitude] */
  center: Array<Scalars['Float']['output']>;
  /** location type, Point, Polygon, etc. */
  type: Scalars['String']['output'];
};

/** Inputs */
export type LocationInput = {
  /** Center coordinates as [longitude, latitude] */
  center: Array<Scalars['Float']['input']>;
};

export type Menu = {
  __typename?: 'Menu';
  /** Menu currency (e.g., USD, EUR, INR) */
  currency: Scalars['String']['output'];
  /** Array of menu sections */
  sections: Array<MenuSection>;
  /** Menu starting price */
  startingPrice?: Maybe<Scalars['Float']['output']>;
};

export type MenuInput = {
  /** Menu currency (e.g., USD, EUR, INR) */
  currency: Scalars['String']['input'];
  /** Array of menu sections */
  sections: Array<MenuSectionInput>;
  /** Menu starting price */
  startingPrice?: InputMaybe<Scalars['Float']['input']>;
};

export type MenuItem = {
  __typename?: 'MenuItem';
  /** Whether the item is available */
  available: MenuItemAvailability;
  /** Menu item name */
  name: Scalars['String']['output'];
  /** Menu item price */
  price: Scalars['Float']['output'];
};

export enum MenuItemAvailability {
  Available = 'AVAILABLE',
  Unavailable = 'UNAVAILABLE'
}

export type MenuItemInput = {
  /** Whether the item is available */
  available: MenuItemAvailability;
  /** Menu item name */
  name: Scalars['String']['input'];
  /** Menu item price */
  price: Scalars['Float']['input'];
};

export type MenuSection = {
  __typename?: 'MenuSection';
  /** Array of menu items in this section */
  items: Array<MenuItem>;
  /** Section name (e.g., "Bières (pression)50cl", "Pizza", "Plats") */
  name: Scalars['String']['output'];
};

export type MenuSectionInput = {
  /** Array of menu items in this section */
  items: Array<MenuItemInput>;
  /** Section name (e.g., "Bières (pression)50cl", "Pizza", "Plats") */
  name: Scalars['String']['input'];
};

export type Mutation = {
  __typename?: 'Mutation';
  /** Create a new bar */
  createBar: Bar;
  /** Create a new bar category */
  createBarCategory: BarCategory;
  /** Create a new city */
  createCity: City;
  createClub: Club;
  /** Create a new club category */
  createClubCategory: ClubCategory;
  /** Create a new layout */
  createLayout: Layout;
  /** Create a new layout block */
  createLayoutBlock: LayoutBlock;
  /** Create a new neighborhood */
  createNeighborhood: Neighborhood;
  createSignedUploadUrl: SignedUploadUrl;
  /** Create a new tag */
  createTag: Tag;
  /** Delete a bar */
  deleteBar: Bar;
  /** Delete a bar category */
  deleteBarCategory: BarCategory;
  /** Delete a city */
  deleteCity: City;
  /** Delete a club category */
  deleteClubCategory: ClubCategory;
  /** Delete a layout */
  deleteLayout: Layout;
  /** Delete a layout block */
  deleteLayoutBlock: LayoutBlock;
  /** Delete a neighborhood */
  deleteNeighborhood: Neighborhood;
  removeClub: Club;
  /** Remove a tag */
  removeTag: Tag;
  /** Reorder layout blocks */
  reorderLayoutBlocks: Array<LayoutBlock>;
  /** Update an existing bar */
  updateBar: Bar;
  /** Update an existing bar category */
  updateBarCategory: BarCategory;
  /** Update an existing city */
  updateCity: City;
  updateClub: Club;
  /** Update an existing club category */
  updateClubCategory: ClubCategory;
  /** Update an existing layout */
  updateLayout: Layout;
  /** Update an existing layout block */
  updateLayoutBlock: LayoutBlock;
  /** Update an existing neighborhood */
  updateNeighborhood: Neighborhood;
  /** Update a tag */
  updateTag: Tag;
};


export type MutationCreateBarArgs = {
  createBarInput: CreateBarInput;
};


export type MutationCreateBarCategoryArgs = {
  createBarCategoryInput: CreateBarCategoryInput;
};


export type MutationCreateCityArgs = {
  createCityInput: CreateCityInput;
};


export type MutationCreateClubArgs = {
  createClubInput: CreateClubInput;
};


export type MutationCreateClubCategoryArgs = {
  createClubCategoryInput: CreateClubCategoryInput;
};


export type MutationCreateLayoutArgs = {
  createLayoutInput: CreateLayoutInput;
};


export type MutationCreateLayoutBlockArgs = {
  createLayoutBlockInput: CreateLayoutBlockInput;
};


export type MutationCreateNeighborhoodArgs = {
  createNeighborhoodInput: CreateNeighborhoodInput;
};


export type MutationCreateSignedUploadUrlArgs = {
  input: SignedUploadUrlInput;
};


export type MutationCreateTagArgs = {
  createTagInput: CreateTagInput;
};


export type MutationDeleteBarArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteBarCategoryArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteCityArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteClubCategoryArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteLayoutArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteLayoutBlockArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteNeighborhoodArgs = {
  id: Scalars['ID']['input'];
};


export type MutationRemoveClubArgs = {
  id: Scalars['String']['input'];
};


export type MutationRemoveTagArgs = {
  id: Scalars['ID']['input'];
};


export type MutationReorderLayoutBlocksArgs = {
  reorderLayoutBlocksInput: ReorderLayoutBlocksInput;
};


export type MutationUpdateBarArgs = {
  updateBarInput: UpdateBarInput;
};


export type MutationUpdateBarCategoryArgs = {
  updateBarCategoryInput: UpdateBarCategoryInput;
};


export type MutationUpdateCityArgs = {
  updateCityInput: UpdateCityInput;
};


export type MutationUpdateClubArgs = {
  updateClubInput: UpdateClubInput;
};


export type MutationUpdateClubCategoryArgs = {
  updateClubCategoryInput: UpdateClubCategoryInput;
};


export type MutationUpdateLayoutArgs = {
  id: Scalars['ID']['input'];
  updateLayoutInput: UpdateLayoutInput;
};


export type MutationUpdateLayoutBlockArgs = {
  id: Scalars['ID']['input'];
  updateLayoutBlockInput: UpdateLayoutBlockInput;
};


export type MutationUpdateNeighborhoodArgs = {
  updateNeighborhoodInput: UpdateNeighborhoodInput;
};


export type MutationUpdateTagArgs = {
  updateTagInput: UpdateTagInput;
};

export type NearQueryInput = {
  /** Center latitude coordinate */
  latitude: Scalars['Float']['input'];
  /** Center longitude coordinate */
  longitude: Scalars['Float']['input'];
  /** Maximum distance in meters */
  maxDistanceMeters?: InputMaybe<Scalars['Float']['input']>;
};

export type Neighborhood = {
  __typename?: 'Neighborhood';
  /** MongoDB ObjectId */
  _id: Scalars['ID']['output'];
  /** Reference to the city this neighborhood belongs to */
  city: City;
  /** Main cover image of the neighborhood */
  coverImage?: Maybe<Scalars['String']['output']>;
  /** Document creation timestamp */
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  /** Array of neighborhood image URLs */
  images?: Maybe<Array<Scalars['String']['output']>>;
  /** Geographical location of the neighborhood */
  location: Location;
  /** Name of the neighborhood */
  name: Scalars['String']['output'];
  /** SEO-friendly URL slug for the neighborhood */
  slug: Scalars['String']['output'];
  /** Document last update timestamp */
  updatedAt: Scalars['DateTime']['output'];
};

export type NeighborhoodStats = {
  __typename?: 'NeighborhoodStats';
  /** Cover image for the neighborhood */
  coverImage?: Maybe<Scalars['String']['output']>;
  /** Neighborhood ID */
  id: Scalars['String']['output'];
  /** Array of neighborhood image URLs */
  images?: Maybe<Array<Scalars['String']['output']>>;
  /** Neighborhood name */
  name: Scalars['String']['output'];
  /** Neighborhood slug */
  slug: Scalars['String']['output'];
  /** Total number of bars in this neighborhood */
  totalBars: Scalars['Float']['output'];
  /** Total number of clubs in this neighborhood */
  totalClubs: Scalars['Float']['output'];
};

export type NeighborhoodsInput = {
  /** Filter by city ID */
  city?: InputMaybe<Scalars['String']['input']>;
  /** Filter by neighborhood name */
  name?: InputMaybe<Scalars['String']['input']>;
  /** Pagination options */
  pagination?: InputMaybe<PaginationInput>;
};

export type PaginatedBarCategories = {
  __typename?: 'PaginatedBarCategories';
  /** Array of documents */
  docs: Array<BarCategory>;
  /** Whether there is a next page */
  hasNextPage: Scalars['Boolean']['output'];
  /** Whether there is a previous page */
  hasPrevPage: Scalars['Boolean']['output'];
  /** Number of documents per page */
  limit: Scalars['Float']['output'];
  /** Next page number */
  nextPage?: Maybe<Scalars['Float']['output']>;
  /** Current page number */
  page: Scalars['Float']['output'];
  /** Starting index of documents on current page */
  pagingCounter?: Maybe<Scalars['Float']['output']>;
  /** Previous page number */
  prevPage?: Maybe<Scalars['Float']['output']>;
  /** Total number of documents */
  totalDocs: Scalars['Float']['output'];
  /** Total number of pages */
  totalPages: Scalars['Float']['output'];
};

export type PaginatedBars = {
  __typename?: 'PaginatedBars';
  /** Array of documents */
  docs: Array<Bar>;
  /** Whether there is a next page */
  hasNextPage: Scalars['Boolean']['output'];
  /** Whether there is a previous page */
  hasPrevPage: Scalars['Boolean']['output'];
  /** Number of documents per page */
  limit: Scalars['Float']['output'];
  /** Next page number */
  nextPage?: Maybe<Scalars['Float']['output']>;
  /** Current page number */
  page: Scalars['Float']['output'];
  /** Starting index of documents on current page */
  pagingCounter?: Maybe<Scalars['Float']['output']>;
  /** Previous page number */
  prevPage?: Maybe<Scalars['Float']['output']>;
  /** Total number of documents */
  totalDocs: Scalars['Float']['output'];
  /** Total number of pages */
  totalPages: Scalars['Float']['output'];
};

export type PaginatedCities = {
  __typename?: 'PaginatedCities';
  /** Array of documents */
  docs: Array<City>;
  /** Whether there is a next page */
  hasNextPage: Scalars['Boolean']['output'];
  /** Whether there is a previous page */
  hasPrevPage: Scalars['Boolean']['output'];
  /** Number of documents per page */
  limit: Scalars['Float']['output'];
  /** Next page number */
  nextPage?: Maybe<Scalars['Float']['output']>;
  /** Current page number */
  page: Scalars['Float']['output'];
  /** Starting index of documents on current page */
  pagingCounter?: Maybe<Scalars['Float']['output']>;
  /** Previous page number */
  prevPage?: Maybe<Scalars['Float']['output']>;
  /** Total number of documents */
  totalDocs: Scalars['Float']['output'];
  /** Total number of pages */
  totalPages: Scalars['Float']['output'];
};

export type PaginatedClubCategories = {
  __typename?: 'PaginatedClubCategories';
  /** Array of documents */
  docs: Array<ClubCategory>;
  /** Whether there is a next page */
  hasNextPage: Scalars['Boolean']['output'];
  /** Whether there is a previous page */
  hasPrevPage: Scalars['Boolean']['output'];
  /** Number of documents per page */
  limit: Scalars['Float']['output'];
  /** Next page number */
  nextPage?: Maybe<Scalars['Float']['output']>;
  /** Current page number */
  page: Scalars['Float']['output'];
  /** Starting index of documents on current page */
  pagingCounter?: Maybe<Scalars['Float']['output']>;
  /** Previous page number */
  prevPage?: Maybe<Scalars['Float']['output']>;
  /** Total number of documents */
  totalDocs: Scalars['Float']['output'];
  /** Total number of pages */
  totalPages: Scalars['Float']['output'];
};

export type PaginatedClubs = {
  __typename?: 'PaginatedClubs';
  /** Array of documents */
  docs: Array<Club>;
  /** Whether there is a next page */
  hasNextPage: Scalars['Boolean']['output'];
  /** Whether there is a previous page */
  hasPrevPage: Scalars['Boolean']['output'];
  /** Number of documents per page */
  limit: Scalars['Float']['output'];
  /** Next page number */
  nextPage?: Maybe<Scalars['Float']['output']>;
  /** Current page number */
  page: Scalars['Float']['output'];
  /** Starting index of documents on current page */
  pagingCounter?: Maybe<Scalars['Float']['output']>;
  /** Previous page number */
  prevPage?: Maybe<Scalars['Float']['output']>;
  /** Total number of documents */
  totalDocs: Scalars['Float']['output'];
  /** Total number of pages */
  totalPages: Scalars['Float']['output'];
};

export type PaginatedLayoutBlocks = {
  __typename?: 'PaginatedLayoutBlocks';
  /** Array of documents */
  docs: Array<LayoutBlock>;
  /** Whether there is a next page */
  hasNextPage: Scalars['Boolean']['output'];
  /** Whether there is a previous page */
  hasPrevPage: Scalars['Boolean']['output'];
  /** Number of documents per page */
  limit: Scalars['Float']['output'];
  /** Next page number */
  nextPage?: Maybe<Scalars['Float']['output']>;
  /** Current page number */
  page: Scalars['Float']['output'];
  /** Starting index of documents on current page */
  pagingCounter?: Maybe<Scalars['Float']['output']>;
  /** Previous page number */
  prevPage?: Maybe<Scalars['Float']['output']>;
  /** Total number of documents */
  totalDocs: Scalars['Float']['output'];
  /** Total number of pages */
  totalPages: Scalars['Float']['output'];
};

export type PaginatedLayouts = {
  __typename?: 'PaginatedLayouts';
  /** Array of documents */
  docs: Array<Layout>;
  /** Whether there is a next page */
  hasNextPage: Scalars['Boolean']['output'];
  /** Whether there is a previous page */
  hasPrevPage: Scalars['Boolean']['output'];
  /** Number of documents per page */
  limit: Scalars['Float']['output'];
  /** Next page number */
  nextPage?: Maybe<Scalars['Float']['output']>;
  /** Current page number */
  page: Scalars['Float']['output'];
  /** Starting index of documents on current page */
  pagingCounter?: Maybe<Scalars['Float']['output']>;
  /** Previous page number */
  prevPage?: Maybe<Scalars['Float']['output']>;
  /** Total number of documents */
  totalDocs: Scalars['Float']['output'];
  /** Total number of pages */
  totalPages: Scalars['Float']['output'];
};

export type PaginatedNeighborhoods = {
  __typename?: 'PaginatedNeighborhoods';
  /** Array of documents */
  docs: Array<Neighborhood>;
  /** Whether there is a next page */
  hasNextPage: Scalars['Boolean']['output'];
  /** Whether there is a previous page */
  hasPrevPage: Scalars['Boolean']['output'];
  /** Number of documents per page */
  limit: Scalars['Float']['output'];
  /** Next page number */
  nextPage?: Maybe<Scalars['Float']['output']>;
  /** Current page number */
  page: Scalars['Float']['output'];
  /** Starting index of documents on current page */
  pagingCounter?: Maybe<Scalars['Float']['output']>;
  /** Previous page number */
  prevPage?: Maybe<Scalars['Float']['output']>;
  /** Total number of documents */
  totalDocs: Scalars['Float']['output'];
  /** Total number of pages */
  totalPages: Scalars['Float']['output'];
};

export type PaginatedTags = {
  __typename?: 'PaginatedTags';
  /** Array of documents */
  docs: Array<Tag>;
  /** Whether there is a next page */
  hasNextPage: Scalars['Boolean']['output'];
  /** Whether there is a previous page */
  hasPrevPage: Scalars['Boolean']['output'];
  /** Number of documents per page */
  limit: Scalars['Float']['output'];
  /** Next page number */
  nextPage?: Maybe<Scalars['Float']['output']>;
  /** Current page number */
  page: Scalars['Float']['output'];
  /** Starting index of documents on current page */
  pagingCounter?: Maybe<Scalars['Float']['output']>;
  /** Previous page number */
  prevPage?: Maybe<Scalars['Float']['output']>;
  /** Total number of documents */
  totalDocs: Scalars['Float']['output'];
  /** Total number of pages */
  totalPages: Scalars['Float']['output'];
};

export type PaginationInput = {
  /** Number of items per page */
  limit?: Scalars['Float']['input'];
  /** Page number (1-based) */
  page?: Scalars['Float']['input'];
  /** Sort configuration array */
  sortConfig?: Array<SortConfigInput>;
};

export type PresignedFields = {
  __typename?: 'PresignedFields';
  Policy: Scalars['String']['output'];
  acl: Scalars['String']['output'];
  algorithm: Scalars['String']['output'];
  bucket: Scalars['String']['output'];
  credential: Scalars['String']['output'];
  date: Scalars['String']['output'];
  key: Scalars['String']['output'];
  signature: Scalars['String']['output'];
};

export type PriceRangeInput = {
  maxPrice: Scalars['Float']['input'];
  minPrice: Scalars['Float']['input'];
};

export type Query = {
  __typename?: 'Query';
  /** Get a single bar by ID or slug with optional filtering */
  bar: Bar;
  /** Get all bar categories with pagination and filtering */
  barCategories: PaginatedBarCategories;
  /** Get a single bar category by ID or slug */
  barCategory: BarCategory;
  /** Get all bars with pagination and filtering */
  bars: PaginatedBars;
  /** Get bars by specific business hour scenarios */
  barsByBusinessHourScenario: PaginatedBars;
  /** Get bars by specific happy hour scenarios */
  barsByHappyHourScenario: PaginatedBars;
  /** Get all bars as per layout with pagination and filtering */
  barsLayout: Array<PaginatedBars>;
  /** Get all cities with pagination and filtering */
  cities: PaginatedCities;
  /** Get a single city by ID or slug */
  city: City;
  /** Get neighborhoods with bar and club counts for a specific city */
  cityNeighborhoodStats: Array<NeighborhoodStats>;
  /** Get a single club by ID or slug with optional filtering */
  club: Club;
  /** Get all club categories with pagination and filtering */
  clubCategories: PaginatedClubCategories;
  /** Get a single club category by ID, name, or slug */
  clubCategory: ClubCategory;
  /** Get all clubs with pagination and filtering */
  clubs: PaginatedClubs;
  /** Get clubs by specific business hour scenarios */
  clubsByBusinessHourScenario: PaginatedClubs;
  /** Get all clubs as per layout with pagination and filtering */
  clubsLayout: Array<PaginatedClubs>;
  /** Get a single layout by ID or scope with optional filtering */
  layout: Layout;
  /** Get a single layout block by ID */
  layoutBlock: LayoutBlock;
  /** Get all layout blocks with pagination and filtering */
  layoutBlocks: PaginatedLayoutBlocks;
  /** Get all blocks for a specific layout */
  layoutBlocksByLayoutId: PaginatedLayoutBlocks;
  /** Get all layouts with pagination and filtering */
  layouts: PaginatedLayouts;
  /** Get current authenticated user */
  me?: Maybe<UserSession>;
  /** Get current user ID */
  myId?: Maybe<Scalars['String']['output']>;
  /** Get a single neighborhood by ID */
  neighborhood: Neighborhood;
  /** Get all neighborhoods with pagination and filtering */
  neighborhoods: PaginatedNeighborhoods;
  /** Get a single tag by ID or name */
  tag: Tag;
  /** Get all tags with pagination and filtering */
  tags: PaginatedTags;
  /** Get all users */
  users: Array<User>;
};


export type QueryBarArgs = {
  idOrSlug: Scalars['ID']['input'];
  languageInput?: InputMaybe<LanguageInput>;
};


export type QueryBarCategoriesArgs = {
  barCategoriesInput?: InputMaybe<BarCategoriesInput>;
  paginationInput?: InputMaybe<PaginationInput>;
};


export type QueryBarCategoryArgs = {
  id?: InputMaybe<Scalars['ID']['input']>;
  slug?: InputMaybe<Scalars['String']['input']>;
};


export type QueryBarsArgs = {
  barsInput?: InputMaybe<BarsInput>;
  languageInput?: InputMaybe<LanguageInput>;
  paginationInput?: InputMaybe<PaginationInput>;
};


export type QueryBarsByBusinessHourScenarioArgs = {
  languageInput?: InputMaybe<LanguageInput>;
  paginationInput?: InputMaybe<PaginationInput>;
  scenario: BusinessHourScenario;
};


export type QueryBarsByHappyHourScenarioArgs = {
  languageInput?: InputMaybe<LanguageInput>;
  paginationInput?: InputMaybe<PaginationInput>;
  scenario: HappyHourScenario;
};


export type QueryBarsLayoutArgs = {
  barsInput: Array<BarsInput>;
  languageInput?: InputMaybe<LanguageInput>;
  paginationInput?: InputMaybe<PaginationInput>;
};


export type QueryCitiesArgs = {
  citiesInput?: InputMaybe<CitiesInput>;
  paginationInput?: InputMaybe<PaginationInput>;
};


export type QueryCityArgs = {
  id?: InputMaybe<Scalars['ID']['input']>;
  slug?: InputMaybe<Scalars['String']['input']>;
};


export type QueryCityNeighborhoodStatsArgs = {
  cityId: Scalars['ID']['input'];
};


export type QueryClubArgs = {
  idOrSlug: Scalars['ID']['input'];
  languageInput?: InputMaybe<LanguageInput>;
};


export type QueryClubCategoriesArgs = {
  clubCategoriesInput?: InputMaybe<ClubCategoriesInput>;
  paginationInput?: InputMaybe<PaginationInput>;
};


export type QueryClubCategoryArgs = {
  id?: InputMaybe<Scalars['ID']['input']>;
  slug?: InputMaybe<Scalars['String']['input']>;
};


export type QueryClubsArgs = {
  clubsInput?: InputMaybe<ClubsInput>;
  languageInput?: InputMaybe<LanguageInput>;
  paginationInput?: InputMaybe<PaginationInput>;
};


export type QueryClubsByBusinessHourScenarioArgs = {
  paginationInput?: InputMaybe<PaginationInput>;
  scenario: ClubBusinessHourScenario;
};


export type QueryClubsLayoutArgs = {
  clubsInput: Array<ClubsInput>;
  paginationInput?: InputMaybe<PaginationInput>;
};


export type QueryLayoutArgs = {
  idOrScope: Scalars['ID']['input'];
};


export type QueryLayoutBlockArgs = {
  id: Scalars['ID']['input'];
};


export type QueryLayoutBlocksArgs = {
  layoutBlocksInput?: InputMaybe<LayoutBlocksInput>;
  paginationInput?: InputMaybe<PaginationInput>;
};


export type QueryLayoutBlocksByLayoutIdArgs = {
  layoutId: Scalars['ID']['input'];
  paginationInput?: InputMaybe<PaginationInput>;
};


export type QueryLayoutsArgs = {
  layoutsInput?: InputMaybe<LayoutsInput>;
  paginationInput?: InputMaybe<PaginationInput>;
};


export type QueryNeighborhoodArgs = {
  id: Scalars['ID']['input'];
};


export type QueryNeighborhoodsArgs = {
  neighborhoodsInput?: InputMaybe<NeighborhoodsInput>;
  paginationInput?: InputMaybe<PaginationInput>;
};


export type QueryTagArgs = {
  id?: InputMaybe<Scalars['ID']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
};


export type QueryTagsArgs = {
  paginationInput?: InputMaybe<PaginationInput>;
  tagsInput?: InputMaybe<TagsInput>;
};

export type RadiusQueryInput = {
  /** Center latitude coordinate */
  latitude: Scalars['Float']['input'];
  /** Center longitude coordinate */
  longitude: Scalars['Float']['input'];
  /** Radius in meters */
  radiusMeters: Scalars['Float']['input'];
};

export type ReorderLayoutBlocksInput = {
  /** Array of block sequences */
  blockSequences: Array<BlockSequenceInput>;
  /** Layout ID */
  layoutId: Scalars['String']['input'];
};

/** SEO fields for search engine optimization and social media sharing */
export type Seo = {
  __typename?: 'SEO';
  /** Canonical URL to prevent duplicate content issues */
  canonicalUrl?: Maybe<Scalars['String']['output']>;
  /** Meta description for search engine result descriptions (recommended: 150-160 characters) */
  metaDescription?: Maybe<Scalars['String']['output']>;
  /** Meta title for page title tags (recommended: 50-60 characters) */
  metaTitle?: Maybe<Scalars['String']['output']>;
  /** Open Graph description for social media sharing */
  ogDescription?: Maybe<Scalars['String']['output']>;
  /** Open Graph title for social media sharing */
  ogTitle?: Maybe<Scalars['String']['output']>;
  /** SEO keywords/tags for search optimization */
  seoKeywords?: Maybe<Array<Scalars['String']['output']>>;
};

/** Input type for SEO fields */
export type SeoInput = {
  /** Canonical URL to prevent duplicate content issues */
  canonicalUrl?: InputMaybe<Scalars['String']['input']>;
  /** Meta description for search engine result descriptions (recommended: 150-160 characters) */
  metaDescription?: InputMaybe<Scalars['String']['input']>;
  /** Meta title for page title tags (recommended: 50-60 characters) */
  metaTitle?: InputMaybe<Scalars['String']['input']>;
  /** Open Graph description for social media sharing */
  ogDescription?: InputMaybe<Scalars['String']['input']>;
  /** Open Graph title for social media sharing */
  ogTitle?: InputMaybe<Scalars['String']['input']>;
  /** SEO keywords/tags for search optimization */
  seoKeywords?: InputMaybe<Array<Scalars['String']['input']>>;
};

export type SessionInfo = {
  __typename?: 'SessionInfo';
  createdAt: Scalars['DateTime']['output'];
  expiresAt: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  ipAddress: Scalars['String']['output'];
  token: Scalars['String']['output'];
  updatedAt: Scalars['DateTime']['output'];
  userAgent: Scalars['String']['output'];
  userId: Scalars['ID']['output'];
};

export type SignedUploadUrl = {
  __typename?: 'SignedUploadUrl';
  fields: PresignedFields;
  url: Scalars['String']['output'];
};

export type SignedUploadUrlInput = {
  contentType: Scalars['String']['input'];
  expiresIn?: InputMaybe<Scalars['Float']['input']>;
  key: Scalars['String']['input'];
};

export type SortConfigInput = {
  /** Field to sort by */
  field: Scalars['String']['input'];
  /** Sort order: "asc" or "desc" */
  order?: SortOrder;
};

export enum SortOrder {
  Asc = 'ASC',
  Desc = 'DESC'
}

export type Tag = {
  __typename?: 'Tag';
  /** MongoDB ObjectId */
  _id: Scalars['ID']['output'];
  /** Document creation timestamp */
  createdAt: Scalars['DateTime']['output'];
  /** Tag description */
  description?: Maybe<Scalars['String']['output']>;
  /** Tag Icon (support lucide-react) */
  icon?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  /** Tag image */
  image?: Maybe<Scalars['String']['output']>;
  /** Tag name */
  name: Scalars['String']['output'];
  /** Document last update timestamp */
  updatedAt: Scalars['DateTime']['output'];
};

export type TagsInput = {
  /** Filter by tag description */
  description?: InputMaybe<Scalars['String']['input']>;
  /** Filter by tag name */
  name?: InputMaybe<Scalars['String']['input']>;
  /** Pagination options */
  pagination?: InputMaybe<PaginationInput>;
};

export type TimeRangeFilter = {
  /** End time in HHMM format (e.g., 100 for 1:00 AM) */
  endTime: Scalars['Float']['input'];
  /** Start time in HHMM format (e.g., 2330 for 11:30 PM) */
  startTime: Scalars['Float']['input'];
};

export type UpdateBarCategoryInput = {
  /** Bar Category description */
  description?: InputMaybe<Scalars['String']['input']>;
  /** Bar Category Icon (support lucide-react) */
  icon?: InputMaybe<Scalars['String']['input']>;
  /** Bar Category ID */
  id: Scalars['ID']['input'];
  /** Bar Category image */
  image?: InputMaybe<Scalars['String']['input']>;
  /** Bar Category name */
  name?: InputMaybe<Scalars['String']['input']>;
  /** SEO-friendly URL slug for the bar category */
  slug?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateBarInput = {
  /** Bar address */
  address?: InputMaybe<AddressInput>;
  /** Opening hours */
  businessHours?: InputMaybe<BarBusinessHoursInput>;
  /** Bar category IDs */
  categories?: InputMaybe<Array<Scalars['String']['input']>>;
  /** Bar city IDs */
  city?: InputMaybe<Scalars['String']['input']>;
  /** Bar contact information (phone) */
  contact?: InputMaybe<ContactInput>;
  /** Bar cover image URL */
  coverImage?: InputMaybe<Scalars['String']['input']>;
  /** Bar description */
  description?: InputMaybe<Scalars['String']['input']>;
  /** Frequently Asked Questions */
  faqs?: InputMaybe<Array<FaqInput>>;
  /** Whether bar is featured */
  featured?: InputMaybe<Scalars['Boolean']['input']>;
  /** Happy hours schedule */
  happyHours?: InputMaybe<BarHappyHoursInput>;
  id: Scalars['String']['input'];
  /** Array of bar image URLs */
  images?: InputMaybe<Array<Scalars['String']['input']>>;
  language?: InputMaybe<LanguageIsoCode>;
  /** Bar logo URL */
  logo?: InputMaybe<Scalars['String']['input']>;
  /** Bar menu */
  menu?: InputMaybe<BarMenuInput>;
  /** Bar name */
  name?: InputMaybe<Scalars['String']['input']>;
  /** Optional neighborhood ID */
  neighborhood?: InputMaybe<Scalars['String']['input']>;
  /** Bar rating (0-5) */
  rating?: InputMaybe<Scalars['Float']['input']>;
  /** SEO fields for search engine optimization and social media sharing */
  seo?: InputMaybe<SeoInput>;
  /** SEO-friendly URL slug for the bar */
  slug?: InputMaybe<Scalars['String']['input']>;
  /** Bar status */
  status?: InputMaybe<BarStatus>;
  /** Bar tag IDs */
  tags?: InputMaybe<Array<Scalars['String']['input']>>;
};

export type UpdateCityInput = {
  /** Cover image for the city */
  coverImage?: InputMaybe<Scalars['String']['input']>;
  /** Main heading for the city */
  heading?: InputMaybe<Scalars['String']['input']>;
  /** City ID */
  id: Scalars['ID']['input'];
  /** Main image of the city */
  image?: InputMaybe<Scalars['String']['input']>;
  /** Geographical location of the city */
  location?: InputMaybe<LocationInput>;
  /** Name of the city */
  name?: InputMaybe<Scalars['String']['input']>;
  /** SEO-friendly URL slug for the city */
  slug?: InputMaybe<Scalars['String']['input']>;
  /** Sub heading for the city */
  subHeading?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateClubCategoryInput = {
  /** Club Category description */
  description?: InputMaybe<Scalars['String']['input']>;
  /** Club Category Icon (support lucide-react) */
  icon?: InputMaybe<Scalars['String']['input']>;
  /** Club Category ID */
  id: Scalars['ID']['input'];
  /** Club Category image */
  image?: InputMaybe<Scalars['String']['input']>;
  /** Club Category name */
  name?: InputMaybe<Scalars['String']['input']>;
  /** SEO-friendly URL slug for the club category */
  slug?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateClubInput = {
  /** Club address */
  address?: InputMaybe<AddressInput>;
  /** Opening hours */
  businessHours?: InputMaybe<BusinessHoursInput>;
  /** Club category IDs */
  categories?: InputMaybe<Array<Scalars['String']['input']>>;
  /** Club city IDs */
  city?: InputMaybe<Scalars['String']['input']>;
  /** Club contact information (phone) */
  contact?: InputMaybe<ContactInput>;
  /** Club cover image URL */
  coverImage?: InputMaybe<Scalars['String']['input']>;
  /** Club description */
  description?: InputMaybe<Scalars['String']['input']>;
  /** Frequently Asked Questions */
  faqs?: InputMaybe<Array<FaqInput>>;
  /** Whether club is featured */
  featured?: InputMaybe<Scalars['Boolean']['input']>;
  id: Scalars['String']['input'];
  /** Array of club image URLs */
  images?: InputMaybe<Array<Scalars['String']['input']>>;
  /** Club logo URL */
  logo?: InputMaybe<Scalars['String']['input']>;
  /** Club menu */
  menu?: InputMaybe<MenuInput>;
  /** Club name */
  name?: InputMaybe<Scalars['String']['input']>;
  /** Optional neighborhood ID */
  neighborhood?: InputMaybe<Scalars['String']['input']>;
  /** Club rating (0-5) */
  rating?: InputMaybe<Scalars['Float']['input']>;
  /** SEO fields for search engine optimization and social media sharing */
  seo?: InputMaybe<SeoInput>;
  /** SEO-friendly URL slug for the club */
  slug?: InputMaybe<Scalars['String']['input']>;
  /** Club status */
  status?: InputMaybe<ClubStatus>;
  /** Club tag IDs */
  tags?: InputMaybe<Array<Scalars['String']['input']>>;
};

export type UpdateLayoutBlockInput = {
  /** Active status of the block */
  active?: InputMaybe<BlockActiveStatus>;
  /** Block data - can be either GlobalBlock or LocalBlock */
  block?: InputMaybe<GlobalOrLocalInput>;
  blockType?: InputMaybe<BlockType>;
  /** GraphQL filter string */
  filter?: InputMaybe<Scalars['String']['input']>;
  /** Layout ID this block belongs to */
  layoutId?: InputMaybe<Scalars['String']['input']>;
  /** Name of the block */
  name?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateLayoutInput = {
  /** Active status of the layout */
  active?: InputMaybe<LayoutActiveStatus>;
  /** Name of the layout */
  name?: InputMaybe<Scalars['String']['input']>;
  /** Unique scope */
  scope?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateNeighborhoodInput = {
  /** City ID this neighborhood belongs to */
  city?: InputMaybe<Scalars['String']['input']>;
  /** Main cover image of the neighborhood */
  coverImage?: InputMaybe<Scalars['String']['input']>;
  /** Neighborhood ID */
  id: Scalars['ID']['input'];
  /** Array of neighborhood image URLs */
  images?: InputMaybe<Array<Scalars['String']['input']>>;
  /** Geographical location of the neighborhood */
  location?: InputMaybe<LocationInput>;
  /** Name of the neighborhood */
  name?: InputMaybe<Scalars['String']['input']>;
  /** SEO-friendly URL slug for the neighborhood */
  slug?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateTagInput = {
  /** Tag description */
  description?: InputMaybe<Scalars['String']['input']>;
  /** Tag Icon (support lucide-react) */
  icon?: InputMaybe<Scalars['String']['input']>;
  /** Tag name */
  id: Scalars['String']['input'];
  /** Tag image */
  image?: InputMaybe<Scalars['String']['input']>;
  /** Tag name */
  name?: InputMaybe<Scalars['String']['input']>;
};

export type User = {
  __typename?: 'User';
  /** MongoDB ObjectId */
  _id: Scalars['ID']['output'];
  /** Document creation timestamp */
  createdAt: Scalars['DateTime']['output'];
  email: Scalars['String']['output'];
  emailVerified: Scalars['Boolean']['output'];
  id: Scalars['ID']['output'];
  image?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  profile_picture?: Maybe<Scalars['String']['output']>;
  role: UserRoleEnum;
  /** Document last update timestamp */
  updatedAt: Scalars['DateTime']['output'];
};

export enum UserRoleEnum {
  Admin = 'ADMIN',
  User = 'USER'
}

export type UserSession = {
  __typename?: 'UserSession';
  session: SessionInfo;
  user: User;
};

export type BarsQueryVariables = Exact<{
  barsInput?: InputMaybe<BarsInput>;
  paginationInput?: InputMaybe<PaginationInput>;
}>;


export type BarsQuery = { __typename?: 'Query', bars: { __typename?: 'PaginatedBars', totalDocs: number, limit: number, page: number, totalPages: number, prevPage?: number | null, nextPage?: number | null, hasPrevPage: boolean, hasNextPage: boolean, pagingCounter?: number | null, docs: Array<{ __typename?: 'Bar', _id: string, id: string, createdAt: Date, updatedAt: Date, slug: string, name: string, description: string, status: BarStatus, logo?: string | null, coverImage?: string | null, images: Array<string>, featured: boolean, rating: number, address?: { __typename?: 'Address', address: string, metroLine?: string | null, metroStation?: string | null, location: { __typename?: 'Location', center: Array<number> } } | null, categories: Array<{ __typename?: 'BarCategory', name: string, id: string }>, tags: Array<{ __typename?: 'Tag', id: string, name: string }>, city: { __typename?: 'City', id: string, name: string, slug: string }, businessHours?: { __typename?: 'BarBusinessHoursSchedule', schedule: Array<{ __typename?: 'BarDayTiming', day: DayOfWeek, timings: Array<number> }> } | null, happyHours?: { __typename?: 'BarHappyHoursSchedule', schedule: Array<{ __typename?: 'BarDayTiming', day: DayOfWeek, timings: Array<number> }> } | null, contact?: { __typename?: 'Contact', countryCode: string, phone: string } | null, faqs?: Array<{ __typename?: 'FAQ', content: string, title: string }> | null, seo?: { __typename?: 'SEO', metaTitle?: string | null, metaDescription?: string | null, canonicalUrl?: string | null, ogTitle?: string | null, ogDescription?: string | null, seoKeywords?: Array<string> | null } | null, menu?: { __typename?: 'BarMenu', currency: string, startingPrice?: number | null, happyHourStartingPrice?: number | null, sections: Array<{ __typename?: 'BarMenuSection', name: string, items: Array<{ __typename?: 'BarMenuItem', name: string, price: number, available: BarMenuItemAvailability, happyHourPrice?: number | null }> }> } | null }> } };

export type BarCategoriesQueryVariables = Exact<{
  paginationInput?: InputMaybe<PaginationInput>;
}>;


export type BarCategoriesQuery = { __typename?: 'Query', barCategories: { __typename?: 'PaginatedBarCategories', totalDocs: number, limit: number, page: number, totalPages: number, prevPage?: number | null, nextPage?: number | null, hasPrevPage: boolean, hasNextPage: boolean, pagingCounter?: number | null, docs: Array<{ __typename?: 'BarCategory', _id: string, id: string, createdAt: Date, updatedAt: Date, name: string, description?: string | null, image?: string | null, icon?: string | null, slug: string }> } };

export type BarCategoryByNameQueryVariables = Exact<{
  slug?: InputMaybe<Scalars['String']['input']>;
}>;


export type BarCategoryByNameQuery = { __typename?: 'Query', barCategory: { __typename?: 'BarCategory', _id: string, id: string, createdAt: Date, updatedAt: Date, name: string, description?: string | null, image?: string | null, icon?: string | null, slug: string } };

export type BarCategoryByIdQueryVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type BarCategoryByIdQuery = { __typename?: 'Query', barCategory: { __typename?: 'BarCategory', _id: string, id: string, createdAt: Date, updatedAt: Date, name: string, description?: string | null, image?: string | null, icon?: string | null, slug: string } };

export type CitiesQueryVariables = Exact<{
  citiesInput?: InputMaybe<CitiesInput>;
  paginationInput?: InputMaybe<PaginationInput>;
}>;


export type CitiesQuery = { __typename?: 'Query', cities: { __typename?: 'PaginatedCities', totalDocs: number, limit: number, page: number, totalPages: number, prevPage?: number | null, nextPage?: number | null, hasPrevPage: boolean, hasNextPage: boolean, pagingCounter?: number | null, docs: Array<{ __typename?: 'City', _id: string, id: string, createdAt: Date, updatedAt: Date, name: string, image: string, coverImage?: string | null, heading: string, subHeading?: string | null, slug: string }> } };

export type CityStatsQueryVariables = Exact<{
  cityId: Scalars['String']['input'];
}>;


export type CityStatsQuery = { __typename?: 'Query', bars: { __typename?: 'PaginatedBars', totalDocs: number }, clubs: { __typename?: 'PaginatedClubs', totalDocs: number } };

export type CityQueryVariables = Exact<{
  cityId: Scalars['ID']['input'];
}>;


export type CityQuery = { __typename?: 'Query', city: { __typename?: 'City', _id: string, name: string, subHeading?: string | null, coverImage?: string | null, createdAt: Date, heading: string, id: string, image: string, updatedAt: Date, slug: string } };

export type ClubsQueryVariables = Exact<{
  clubsInput?: InputMaybe<ClubsInput>;
  paginationInput?: InputMaybe<PaginationInput>;
}>;


export type ClubsQuery = { __typename?: 'Query', clubs: { __typename?: 'PaginatedClubs', totalDocs: number, limit: number, page: number, totalPages: number, prevPage?: number | null, nextPage?: number | null, hasPrevPage: boolean, hasNextPage: boolean, pagingCounter?: number | null, docs: Array<{ __typename?: 'Club', _id: string, id: string, createdAt: Date, updatedAt: Date, slug: string, name: string, description: string, status: ClubStatus, logo?: string | null, coverImage?: string | null, images: Array<string>, featured: boolean, rating: number, address?: { __typename?: 'Address', address: string, metroLine?: string | null, metroStation?: string | null, location: { __typename?: 'Location', center: Array<number> } } | null, categories: Array<{ __typename?: 'ClubCategory', name: string, id: string }>, city: { __typename?: 'City', id: string, name: string, slug: string }, tags: Array<{ __typename?: 'Tag', id: string, name: string }>, businessHours?: { __typename?: 'BusinessHoursSchedule', schedule: Array<{ __typename?: 'DayTiming', day: DayOfWeek, timings: Array<number> }> } | null, contact?: { __typename?: 'Contact', countryCode: string, phone: string } | null, faqs?: Array<{ __typename?: 'FAQ', content: string, title: string }> | null, seo?: { __typename?: 'SEO', metaTitle?: string | null, metaDescription?: string | null, canonicalUrl?: string | null, ogTitle?: string | null, ogDescription?: string | null, seoKeywords?: Array<string> | null } | null, menu?: { __typename?: 'Menu', currency: string, startingPrice?: number | null, sections: Array<{ __typename?: 'MenuSection', name: string, items: Array<{ __typename?: 'MenuItem', name: string, price: number, available: MenuItemAvailability }> }> } | null }> } };

export type ClubCategoriesQueryVariables = Exact<{
  paginationInput?: InputMaybe<PaginationInput>;
}>;


export type ClubCategoriesQuery = { __typename?: 'Query', clubCategories: { __typename?: 'PaginatedClubCategories', totalDocs: number, limit: number, page: number, totalPages: number, prevPage?: number | null, nextPage?: number | null, hasPrevPage: boolean, hasNextPage: boolean, pagingCounter?: number | null, docs: Array<{ __typename?: 'ClubCategory', _id: string, id: string, createdAt: Date, updatedAt: Date, name: string, description?: string | null, image?: string | null, icon?: string | null, slug: string }> } };

export type ClubCategoryByNameQueryVariables = Exact<{
  slug?: InputMaybe<Scalars['String']['input']>;
}>;


export type ClubCategoryByNameQuery = { __typename?: 'Query', clubCategory: { __typename?: 'ClubCategory', _id: string, id: string, createdAt: Date, updatedAt: Date, name: string, description?: string | null, image?: string | null, icon?: string | null, slug: string } };

export type ClubCategoryByIdQueryVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type ClubCategoryByIdQuery = { __typename?: 'Query', clubCategory: { __typename?: 'ClubCategory', _id: string, id: string, createdAt: Date, updatedAt: Date, name: string, description?: string | null, image?: string | null, icon?: string | null, slug: string } };

export type CityNeighborhoodStatsQueryVariables = Exact<{
  cityId: Scalars['ID']['input'];
}>;


export type CityNeighborhoodStatsQuery = { __typename?: 'Query', cityNeighborhoodStats: Array<{ __typename?: 'NeighborhoodStats', coverImage?: string | null, id: string, images?: Array<string> | null, name: string, slug: string, totalBars: number, totalClubs: number }> };



export const BarsDocument = `
    query Bars($barsInput: BarsInput, $paginationInput: PaginationInput) {
  bars(barsInput: $barsInput, paginationInput: $paginationInput) {
    totalDocs
    limit
    page
    totalPages
    prevPage
    nextPage
    hasPrevPage
    hasNextPage
    pagingCounter
    docs {
      _id
      id
      createdAt
      updatedAt
      slug
      name
      description
      status
      logo
      coverImage
      images
      featured
      rating
      address {
        address
        metroLine
        metroStation
        location {
          center
        }
      }
      categories {
        name
        id
      }
      tags {
        id
        name
      }
      city {
        id
        name
        slug
      }
      businessHours {
        schedule {
          day
          timings
        }
      }
      happyHours {
        schedule {
          day
          timings
        }
      }
      contact {
        countryCode
        phone
      }
      faqs {
        content
        title
      }
      seo {
        metaTitle
        metaDescription
        canonicalUrl
        ogTitle
        ogDescription
        seoKeywords
      }
      menu {
        currency
        startingPrice
        happyHourStartingPrice
        sections {
          name
          items {
            name
            price
            available
            happyHourPrice
          }
        }
      }
    }
  }
}
    `;

export const useBarsQuery = <
      TData = BarsQuery,
      TError = Error
    >(
      variables?: BarsQueryVariables,
      options?: Omit<UseQueryOptions<BarsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<BarsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<BarsQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['Bars'] : ['Bars', variables],
    queryFn: fetchData<BarsQuery, BarsQueryVariables>(BarsDocument, variables),
    ...options
  }
    )};

useBarsQuery.document = BarsDocument;

useBarsQuery.getKey = (variables?: BarsQueryVariables) => variables === undefined ? ['Bars'] : ['Bars', variables];

export const BarCategoriesDocument = `
    query BarCategories($paginationInput: PaginationInput) {
  barCategories(paginationInput: $paginationInput) {
    totalDocs
    limit
    page
    totalPages
    prevPage
    nextPage
    hasPrevPage
    hasNextPage
    pagingCounter
    docs {
      _id
      id
      createdAt
      updatedAt
      name
      description
      image
      icon
      slug
    }
  }
}
    `;

export const useBarCategoriesQuery = <
      TData = BarCategoriesQuery,
      TError = Error
    >(
      variables?: BarCategoriesQueryVariables,
      options?: Omit<UseQueryOptions<BarCategoriesQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<BarCategoriesQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<BarCategoriesQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['BarCategories'] : ['BarCategories', variables],
    queryFn: fetchData<BarCategoriesQuery, BarCategoriesQueryVariables>(BarCategoriesDocument, variables),
    ...options
  }
    )};

useBarCategoriesQuery.document = BarCategoriesDocument;

useBarCategoriesQuery.getKey = (variables?: BarCategoriesQueryVariables) => variables === undefined ? ['BarCategories'] : ['BarCategories', variables];

export const BarCategoryByNameDocument = `
    query BarCategoryByName($slug: String) {
  barCategory(id: null, slug: $slug) {
    _id
    id
    createdAt
    updatedAt
    name
    description
    image
    icon
    slug
  }
}
    `;

export const useBarCategoryByNameQuery = <
      TData = BarCategoryByNameQuery,
      TError = Error
    >(
      variables?: BarCategoryByNameQueryVariables,
      options?: Omit<UseQueryOptions<BarCategoryByNameQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<BarCategoryByNameQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<BarCategoryByNameQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['BarCategoryByName'] : ['BarCategoryByName', variables],
    queryFn: fetchData<BarCategoryByNameQuery, BarCategoryByNameQueryVariables>(BarCategoryByNameDocument, variables),
    ...options
  }
    )};

useBarCategoryByNameQuery.document = BarCategoryByNameDocument;

useBarCategoryByNameQuery.getKey = (variables?: BarCategoryByNameQueryVariables) => variables === undefined ? ['BarCategoryByName'] : ['BarCategoryByName', variables];

export const BarCategoryByIdDocument = `
    query BarCategoryByID($id: ID!) {
  barCategory(id: $id) {
    _id
    id
    createdAt
    updatedAt
    name
    description
    image
    icon
    slug
  }
}
    `;

export const useBarCategoryByIdQuery = <
      TData = BarCategoryByIdQuery,
      TError = Error
    >(
      variables: BarCategoryByIdQueryVariables,
      options?: Omit<UseQueryOptions<BarCategoryByIdQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<BarCategoryByIdQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<BarCategoryByIdQuery, TError, TData>(
      {
    queryKey: ['BarCategoryByID', variables],
    queryFn: fetchData<BarCategoryByIdQuery, BarCategoryByIdQueryVariables>(BarCategoryByIdDocument, variables),
    ...options
  }
    )};

useBarCategoryByIdQuery.document = BarCategoryByIdDocument;

useBarCategoryByIdQuery.getKey = (variables: BarCategoryByIdQueryVariables) => ['BarCategoryByID', variables];

export const CitiesDocument = `
    query Cities($citiesInput: CitiesInput, $paginationInput: PaginationInput) {
  cities(citiesInput: $citiesInput, paginationInput: $paginationInput) {
    totalDocs
    limit
    page
    totalPages
    prevPage
    nextPage
    hasPrevPage
    hasNextPage
    pagingCounter
    docs {
      _id
      id
      createdAt
      updatedAt
      name
      image
      coverImage
      heading
      subHeading
      slug
    }
  }
}
    `;

export const useCitiesQuery = <
      TData = CitiesQuery,
      TError = Error
    >(
      variables?: CitiesQueryVariables,
      options?: Omit<UseQueryOptions<CitiesQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<CitiesQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<CitiesQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['Cities'] : ['Cities', variables],
    queryFn: fetchData<CitiesQuery, CitiesQueryVariables>(CitiesDocument, variables),
    ...options
  }
    )};

useCitiesQuery.document = CitiesDocument;

useCitiesQuery.getKey = (variables?: CitiesQueryVariables) => variables === undefined ? ['Cities'] : ['Cities', variables];

export const CityStatsDocument = `
    query CityStats($cityId: String!) {
  bars(barsInput: {city: $cityId}) {
    totalDocs
  }
  clubs(clubsInput: {city: $cityId}) {
    totalDocs
  }
}
    `;

export const useCityStatsQuery = <
      TData = CityStatsQuery,
      TError = Error
    >(
      variables: CityStatsQueryVariables,
      options?: Omit<UseQueryOptions<CityStatsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<CityStatsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<CityStatsQuery, TError, TData>(
      {
    queryKey: ['CityStats', variables],
    queryFn: fetchData<CityStatsQuery, CityStatsQueryVariables>(CityStatsDocument, variables),
    ...options
  }
    )};

useCityStatsQuery.document = CityStatsDocument;

useCityStatsQuery.getKey = (variables: CityStatsQueryVariables) => ['CityStats', variables];

export const CityDocument = `
    query City($cityId: ID!) {
  city(id: $cityId) {
    _id
    name
    subHeading
    coverImage
    createdAt
    heading
    id
    image
    updatedAt
    slug
  }
}
    `;

export const useCityQuery = <
      TData = CityQuery,
      TError = Error
    >(
      variables: CityQueryVariables,
      options?: Omit<UseQueryOptions<CityQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<CityQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<CityQuery, TError, TData>(
      {
    queryKey: ['City', variables],
    queryFn: fetchData<CityQuery, CityQueryVariables>(CityDocument, variables),
    ...options
  }
    )};

useCityQuery.document = CityDocument;

useCityQuery.getKey = (variables: CityQueryVariables) => ['City', variables];

export const ClubsDocument = `
    query Clubs($clubsInput: ClubsInput, $paginationInput: PaginationInput) {
  clubs(clubsInput: $clubsInput, paginationInput: $paginationInput) {
    totalDocs
    limit
    page
    totalPages
    prevPage
    nextPage
    hasPrevPage
    hasNextPage
    pagingCounter
    docs {
      _id
      id
      createdAt
      updatedAt
      slug
      name
      description
      status
      logo
      coverImage
      images
      featured
      rating
      address {
        address
        metroLine
        metroStation
        location {
          center
        }
      }
      categories {
        name
        id
      }
      city {
        id
        name
        slug
      }
      tags {
        id
        name
      }
      businessHours {
        schedule {
          day
          timings
        }
      }
      contact {
        countryCode
        phone
      }
      faqs {
        content
        title
      }
      seo {
        metaTitle
        metaDescription
        canonicalUrl
        ogTitle
        ogDescription
        seoKeywords
      }
      menu {
        currency
        startingPrice
        sections {
          name
          items {
            name
            price
            available
          }
        }
      }
    }
  }
}
    `;

export const useClubsQuery = <
      TData = ClubsQuery,
      TError = Error
    >(
      variables?: ClubsQueryVariables,
      options?: Omit<UseQueryOptions<ClubsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<ClubsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<ClubsQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['Clubs'] : ['Clubs', variables],
    queryFn: fetchData<ClubsQuery, ClubsQueryVariables>(ClubsDocument, variables),
    ...options
  }
    )};

useClubsQuery.document = ClubsDocument;

useClubsQuery.getKey = (variables?: ClubsQueryVariables) => variables === undefined ? ['Clubs'] : ['Clubs', variables];

export const ClubCategoriesDocument = `
    query ClubCategories($paginationInput: PaginationInput) {
  clubCategories(paginationInput: $paginationInput) {
    totalDocs
    limit
    page
    totalPages
    prevPage
    nextPage
    hasPrevPage
    hasNextPage
    pagingCounter
    docs {
      _id
      id
      createdAt
      updatedAt
      name
      description
      image
      icon
      slug
    }
  }
}
    `;

export const useClubCategoriesQuery = <
      TData = ClubCategoriesQuery,
      TError = Error
    >(
      variables?: ClubCategoriesQueryVariables,
      options?: Omit<UseQueryOptions<ClubCategoriesQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<ClubCategoriesQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<ClubCategoriesQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['ClubCategories'] : ['ClubCategories', variables],
    queryFn: fetchData<ClubCategoriesQuery, ClubCategoriesQueryVariables>(ClubCategoriesDocument, variables),
    ...options
  }
    )};

useClubCategoriesQuery.document = ClubCategoriesDocument;

useClubCategoriesQuery.getKey = (variables?: ClubCategoriesQueryVariables) => variables === undefined ? ['ClubCategories'] : ['ClubCategories', variables];

export const ClubCategoryByNameDocument = `
    query ClubCategoryByName($slug: String) {
  clubCategory(id: null, slug: $slug) {
    _id
    id
    createdAt
    updatedAt
    name
    description
    image
    icon
    slug
  }
}
    `;

export const useClubCategoryByNameQuery = <
      TData = ClubCategoryByNameQuery,
      TError = Error
    >(
      variables?: ClubCategoryByNameQueryVariables,
      options?: Omit<UseQueryOptions<ClubCategoryByNameQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<ClubCategoryByNameQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<ClubCategoryByNameQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['ClubCategoryByName'] : ['ClubCategoryByName', variables],
    queryFn: fetchData<ClubCategoryByNameQuery, ClubCategoryByNameQueryVariables>(ClubCategoryByNameDocument, variables),
    ...options
  }
    )};

useClubCategoryByNameQuery.document = ClubCategoryByNameDocument;

useClubCategoryByNameQuery.getKey = (variables?: ClubCategoryByNameQueryVariables) => variables === undefined ? ['ClubCategoryByName'] : ['ClubCategoryByName', variables];

export const ClubCategoryByIdDocument = `
    query ClubCategoryByID($id: ID!) {
  clubCategory(id: $id) {
    _id
    id
    createdAt
    updatedAt
    name
    description
    image
    icon
    slug
  }
}
    `;

export const useClubCategoryByIdQuery = <
      TData = ClubCategoryByIdQuery,
      TError = Error
    >(
      variables: ClubCategoryByIdQueryVariables,
      options?: Omit<UseQueryOptions<ClubCategoryByIdQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<ClubCategoryByIdQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<ClubCategoryByIdQuery, TError, TData>(
      {
    queryKey: ['ClubCategoryByID', variables],
    queryFn: fetchData<ClubCategoryByIdQuery, ClubCategoryByIdQueryVariables>(ClubCategoryByIdDocument, variables),
    ...options
  }
    )};

useClubCategoryByIdQuery.document = ClubCategoryByIdDocument;

useClubCategoryByIdQuery.getKey = (variables: ClubCategoryByIdQueryVariables) => ['ClubCategoryByID', variables];

export const CityNeighborhoodStatsDocument = `
    query CityNeighborhoodStats($cityId: ID!) {
  cityNeighborhoodStats(cityId: $cityId) {
    coverImage
    id
    images
    name
    slug
    totalBars
    totalClubs
  }
}
    `;

export const useCityNeighborhoodStatsQuery = <
      TData = CityNeighborhoodStatsQuery,
      TError = Error
    >(
      variables: CityNeighborhoodStatsQueryVariables,
      options?: Omit<UseQueryOptions<CityNeighborhoodStatsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<CityNeighborhoodStatsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<CityNeighborhoodStatsQuery, TError, TData>(
      {
    queryKey: ['CityNeighborhoodStats', variables],
    queryFn: fetchData<CityNeighborhoodStatsQuery, CityNeighborhoodStatsQueryVariables>(CityNeighborhoodStatsDocument, variables),
    ...options
  }
    )};

useCityNeighborhoodStatsQuery.document = CityNeighborhoodStatsDocument;

useCityNeighborhoodStatsQuery.getKey = (variables: CityNeighborhoodStatsQueryVariables) => ['CityNeighborhoodStats', variables];
