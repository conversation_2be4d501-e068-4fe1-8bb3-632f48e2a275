"use client";

import * as AccordionPrimitive from "@radix-ui/react-accordion";
import * as React from "react";

import COLORS from "@/constants/colors";
import { cn } from "@/lib/utils";
import ICONS from "@/public/icons";

function Accordion({
  ...props
}: React.ComponentProps<typeof AccordionPrimitive.Root>) {
  return <AccordionPrimitive.Root data-slot="accordion" {...props} />;
}

function AccordionItem({
  className,
  ...props
}: React.ComponentProps<typeof AccordionPrimitive.Item>) {
  return (
    <AccordionPrimitive.Item
      data-slot="accordion-item"
      className={cn("border-b last:border-b-0", className)}
      {...props}
    />
  );
}

function AccordionTrigger({
  className,
  chevron = true,
  children,
  ...props
}: React.ComponentProps<typeof AccordionPrimitive.Trigger> & {
  chevron?: boolean;
}) {
  return (
    <AccordionPrimitive.Header className="flex">
      <AccordionPrimitive.Trigger
        className={cn(
          "group flex flex-row items-center gap-4 rounded-md py-4 text-left text-sm font-medium transition-all outline-none",
          "focus-visible:ring-1 focus-visible:ring-offset-1 disabled:pointer-events-none w-full cursor-pointer",
          "data-[state=open]:svg:rotate-180", // rotate only for chevron
          className
        )}
        {...props}
      >
        {children}
        {/* Chevron icon, always shown, only rotates */}
        {chevron && (
          <ICONS.ChevronDownIcon
            width={15}
            height={8}
            color={COLORS.LIGHT.TEXT}
            className="text-muted-foreground transition-transform duration-200 ease-in-out shrink-0"
          />
        )}
      </AccordionPrimitive.Trigger>
    </AccordionPrimitive.Header>
  );
}

function AccordionContent({
  className,
  children,
  ...props
}: React.ComponentProps<typeof AccordionPrimitive.Content>) {
  return (
    <AccordionPrimitive.Content
      data-slot="accordion-content"
      className="data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm"
      {...props}
    >
      <div className={cn("pt-0 pb-4", className)}>{children}</div>
    </AccordionPrimitive.Content>
  );
}

export { Accordion, AccordionContent, AccordionItem, AccordionTrigger };
