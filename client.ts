import axios from "axios";
import { getDefaultStore } from "jotai";
import { NEXT_API_URL } from "./env";
import { tokenAtom } from "./hooks/useAuth";

export function getToken() {
  return getDefaultStore().get(tokenAtom);
}

export const client = axios.create({ baseURL: NEXT_API_URL });

// @ts-ignore
const devToken =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2ODZhOTJkNjZjMTZlM2VlYTkyYzExYjMiLCJpYXQiOjE3NTI4Mzk3OTV9.hCtT1RH-dxkFXypTfl2HoY2brtH_CQIAV82AP6MsdTs";
// @ts-ignore
const prodToken =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2ODZhOTJkNjZjMTZlM2VlYTkyYzExYjMiLCJpYXQiOjE3NTI4Mzk3OTV9.hCtT1RH-dxkFXypTfl2HoY2brtH_CQIAV82AP6MsdTs";

// Request Interceptor to attach token
client.interceptors.request.use(
  (config) => {
    // const token = getToken(); // Retrieve token from storage
    const token = process.env.NODE_ENV === "development" ? devToken : prodToken; // Example token, replace with actual token retrieval logic
    if (token) {
      config.headers.Authorization = `Bearer ${token}`; // Inject token in headers
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

export const fetchData = <TData, TVariables>(
  query: string,
  variables?: TVariables,
  options?: Record<string, string>
): (() => Promise<TData>) => {
  return async () => {
    try {
      const headers = {
        "Content-Type": "application/json",
        ...options,
      };

      const requestBody = {
        query,
        variables,
      };

      const response = await client.post("/graphql", requestBody, { headers });

      if (response.data.errors) {
        const { message } = response.data.errors[0] || {};
        throw new Error(message || "Error…");
      }

      return response.data.data;
    } catch (error) {
      console.error("GraphQL request failed:", error);
      throw error;
    }
  };
};

/** File Upload */

type UploadFile = {
  file: File;
  parentFolderId?: string;
  fileName?: string;
};

type FileUploadResponse = {
  success: boolean;
  message: string;
  filePath: string;
  fileId: string;
};

export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

// Uncomment the following code to enable file upload functionality

// export const uploadFile = async (
//   { file, fileName, parentFolderId }: UploadFile,
//   uploadProgress?: (progress: UploadProgress) => void
// ): Promise<FileUploadResponse> => {
//   const formData = new FormData();
//   formData.append("file", file);
//   formData.append("fileName", fileName || file.name);
//   if (parentFolderId) formData.append("parentFolderId", parentFolderId);
//   const { data } = await client.post<FileUploadResponse>(
//     "/storage/upload",
//     formData,
//     {
//       headers: { "Content-Type": "multipart/form-data" },
//       onUploadProgress(progressEvent) {
//         if (progressEvent.total) {
//           const progress: UploadProgress = {
//             loaded: progressEvent.loaded,
//             total: progressEvent.total,
//             percentage: Math.round(
//               (progressEvent.loaded / progressEvent.total) * 100
//             ),
//           };
//           uploadProgress?.(progress);
//         }
//       },
//     }
//   );

//   return data;
// };
