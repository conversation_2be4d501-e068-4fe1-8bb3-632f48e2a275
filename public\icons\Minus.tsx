import { IconProps } from "@/types";
import * as React from "react";

const MinusIcon = ({ className, color, height, width }: IconProps) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 20 4"
    fill="none"
    className={className}
  >
    <path
      d="M0.666992 2.00016C0.666992 1.35581 1.18933 0.833496 1.83366 0.833496H18.167C18.8113 0.833496 19.3337 1.35581 19.3337 2.00016C19.3337 2.64451 18.8113 3.16683 18.167 3.16683H1.83366C1.18933 3.16683 0.666992 2.64451 0.666992 2.00016Z"
      fill={color}
    />
  </svg>
);
export default MinusIcon;
