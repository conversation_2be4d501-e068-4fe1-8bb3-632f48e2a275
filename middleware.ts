import createMiddleware from "next-intl/middleware";
import { routing } from "./i18n/routing";

export default createMiddleware(routing);

export const config = {
  // More specific matcher that excludes favicon and other static assets
  matcher: [
    // Enable a redirect to a matching locale at the root
    "/",

    // Set a cookie to remember the previous locale for
    // all requests that have a locale prefix
    "/(de|en)/:path*",

    // Enable redirects that add missing locales
    // but exclude static files, API routes, and Next.js internals
    "/((?!api|trpc|_next|_vercel|favicon\\.ico|.*\\..*).*)",
  ],
};
