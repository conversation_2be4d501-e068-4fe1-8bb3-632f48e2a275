import { IconProps } from "@/types";
import * as React from "react";
const BeerJugsIcon = ({ className, color, height, width }: IconProps) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 27 25"
    fill="none"
    className={className}
  >
    <path
      d="M26.1055 18.2364C25.8176 19.4399 24.8206 20.0405 23.6034 20.1103C23.6759 20.7986 23.8309 21.306 23.5757 21.9793C23.278 22.7651 22.5959 23.1757 21.7938 23.3578C19.8035 23.8099 17.6372 23.9927 15.6229 24.3816C14.5348 24.2836 14.0581 23.8592 13.587 22.9598C13.4787 22.7536 13.4843 23.0134 13.4375 23.1095C13.0556 23.8941 12.283 24.3378 11.3992 24.3816C9.39099 23.9764 7.21672 23.8081 5.2319 23.3494C4.35481 23.1468 3.59274 22.5925 3.38915 21.6944C3.26553 21.1473 3.4279 20.6543 3.46296 20.1097C2.28203 20.0519 1.11463 19.3912 0.965786 18.1582C0.883367 17.4747 1.29792 15.3838 1.48367 14.6431C1.88162 13.0566 3.25999 12.5679 4.79028 13.0037C4.91821 12.5582 4.72016 12.5817 4.49751 12.3063C2.70827 10.097 4.57624 7.07909 7.39509 7.60212C7.51872 7.62496 7.8933 7.77345 7.95112 7.76203C7.99417 7.75361 8.49668 7.34782 8.62462 7.27207C10.019 6.44605 11.7867 6.56809 13.015 7.62977C13.1165 7.71754 13.4769 8.14799 13.5126 8.154C13.5974 8.16903 13.8502 7.81073 13.948 7.72295C15.1991 6.59935 16.9865 6.40096 18.4577 7.25343C18.5924 7.33158 19.106 7.7488 19.1546 7.75301C19.2358 7.76023 19.833 7.55643 20.0446 7.53839C22.5067 7.32798 24.1502 10.005 22.7749 12.031C22.6679 12.1885 22.2779 12.5522 22.2459 12.622C22.1961 12.732 22.3031 12.8823 22.273 13.0037C23.7424 12.5769 25.1521 13.0332 25.5593 14.5674L26.103 17.42V18.2358L26.1055 18.2364ZM12.1582 8.92351C11.4127 8.04699 10.0473 7.97545 9.16218 8.70769C8.74886 9.04916 8.58525 9.61967 7.89391 9.42008C7.29115 9.24574 7.17982 8.85137 6.34148 9.0714C4.60945 9.52589 5.20914 12.2781 7.15891 11.7905C7.69525 11.6565 8.03477 11.0763 8.64737 11.4683C9.09207 11.7527 9.43528 12.447 10.1444 12.4284C9.71636 10.9669 10.6531 9.30766 12.1582 8.92411V8.92351ZM11.9792 10.6693C10.8044 11.8176 12.2517 13.742 13.7457 12.9003C14.1799 12.6556 14.2937 12.0953 14.8177 12.0286C15.2304 11.9763 15.5368 12.3069 15.939 12.4049C16.6611 12.5811 17.3752 12.4278 17.9534 11.9733C18.3747 11.642 18.5389 11.1467 19.2149 11.4112C19.8312 11.6523 19.9591 12.0346 20.79 11.7701C22.3751 11.2657 21.8916 8.79185 20.0932 9.02451C19.6011 9.08823 19.3612 9.52589 18.8237 9.44052C18.4245 9.3774 18.2486 8.94335 17.9534 8.70769C16.9797 7.93096 15.4845 8.07104 14.7495 9.08102C14.4167 9.53791 14.3804 10.3645 13.6559 10.3675C13.483 10.3681 13.2911 10.2822 13.1146 10.2689C12.7013 10.2383 12.272 10.382 11.9786 10.6687L11.9792 10.6693ZM12.0026 14.4045C11.7578 14.393 11.2491 13.9391 11.0996 13.9157C10.9348 13.8899 10.5221 13.9818 10.2681 13.9638C9.75327 13.9277 9.1505 13.7269 8.70519 13.475C8.56926 13.3981 8.17746 13.0789 8.09443 13.0614C8.01139 13.044 7.44614 13.2592 7.26593 13.2875C6.92764 13.3404 6.60904 13.3151 6.27136 13.2995L4.83949 21.1437C4.84687 21.6799 5.15009 21.8122 5.61632 21.9186C7.46829 22.3424 9.48387 22.4849 11.35 22.8943C12.029 22.8703 12.0788 22.4681 12.1914 21.9421C12.4171 20.8852 12.5524 19.8043 12.7511 18.742L12.0026 14.4051V14.4045ZM20.795 13.2989C20.4628 13.3284 20.1824 13.3404 19.8496 13.2869C19.6393 13.2532 19.1773 13.0554 19.0568 13.0572C18.9977 13.0578 18.2314 13.5538 18.0352 13.6368C17.4853 13.8694 16.8106 14.0125 16.2103 13.9638C15.9445 13.9422 15.284 13.7023 15.1314 13.7233C15.0841 13.7299 14.5256 14.1772 14.3521 14.2614C14.0975 14.3846 13.8213 14.4532 13.5532 14.5385L14.9703 22.4242C15.0736 22.7182 15.4414 22.9358 15.76 22.8877C17.612 22.4795 19.6122 22.3394 21.4494 21.9186C21.939 21.8068 22.249 21.6589 22.2201 21.0998L20.7944 13.2989H20.795ZM4.4932 14.4477C3.98393 14.354 3.26491 14.1261 3.02134 14.7537C2.87988 15.1181 2.45056 17.4488 2.45241 17.8366C2.4561 18.567 3.21386 18.5748 3.76004 18.6686L4.4932 14.4477ZM23.3063 18.6686C23.8869 18.5598 24.5309 18.5977 24.6145 17.8697C24.3704 16.945 24.3716 15.6669 24.0579 14.7898C23.8248 14.1375 23.0867 14.3191 22.5658 14.4736L23.3063 18.6692V18.6686Z"
      fill={color}
    />
    <path
      d="M9.83743 2.64711C10.0902 2.59601 10.3123 2.63509 10.5257 2.76855C10.765 2.91764 11.7146 3.87051 11.8044 4.11038C12.0652 4.80474 11.3345 5.35122 10.6998 4.99832C10.5423 4.91055 9.60186 3.99556 9.48561 3.83324C9.19284 3.42384 9.30724 2.75472 9.83743 2.64711Z"
      fill={color}
    />
    <path
      d="M16.81 2.64769C17.6121 2.5016 18.0266 3.2657 17.5364 3.88552C17.387 4.07489 16.6144 4.83779 16.4268 4.96103C15.6666 5.46241 14.7766 4.64481 15.4716 3.78753C15.6678 3.54585 16.5369 2.69699 16.8094 2.64709L16.81 2.64769Z"
      fill={color}
    />
    <path
      d="M13.4234 0.966742C13.8343 0.907827 14.223 1.2024 14.2704 1.60399C14.3073 1.91841 14.3183 3.0847 14.2593 3.36906C14.1154 4.06041 12.8914 4.05861 12.796 3.28489C12.7585 2.97829 12.7536 1.75489 12.8182 1.48015C12.8779 1.22826 13.1669 1.00281 13.4234 0.966141V0.966742Z"
      fill={color}
    />
  </svg>
);
export default BeerJugsIcon;
