"use client";

import Link from "next/link";
import { useEffect, useState } from "react";

export default function DownloadButton() {
  const [storeUrl, setStoreUrl] = useState<string>("/#download-app");

  useEffect(() => {
    if (typeof navigator !== "undefined") {
      const ua = navigator.userAgent || navigator.vendor;

      if (/android/i.test(ua)) {
        // Android → Play Store
        setStoreUrl(
          "https://play.google.com/store/apps/details?id=com.seekersocial.app&hl=en_IN"
        );
      } else if (/iPad|iPhone|iPod/.test(ua)) {
        // iOS → App Store
        setStoreUrl(
          "https://apps.apple.com/fr/app/seeker-social-find-nightlife/id6749886146"
        );
      } else {
        // Fallback → your landing section
        setStoreUrl("/#download-app");
      }
    }
  }, []);

  return (
    <Link
      href={storeUrl}
      target="_blank"
      rel="noopener noreferrer"
      className="px-4 ml-4 lg:px-7 py-3.5 rounded-full bg-gradient-to-br from-muted-primary via-primary to-primary font-semibold text-white flex items-center justify-center"
    >
      DOWNLOAD APP
    </Link>
  );
}
