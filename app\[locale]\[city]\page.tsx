import { fetchData } from "@/client";
import LandingPage from "@/elements/landing-page";
import {
  BarsDocument,
  BarsQuery,
  BarsQueryVariables,
  CitiesDocument,
  CitiesQuery,
  CitiesQueryVariables,
  ClubsDocument,
  ClubsQuery,
  ClubsQueryVariables,
} from "@/generated/graphql";

import { Metadata } from "next";
import NotFound from "../not-found";

export const revalidate = 3600;

export const generateStaticParams = async () => {
  const citiesRes = await fetchData<CitiesQuery, CitiesQueryVariables>(
    CitiesDocument
  )();

  const city = citiesRes?.cities?.docs?.[0];
  if (!city) {
    throw new Error("City data not found");
  }

  return [
    {
      cityId: city.name,
    },
  ];
};

export const generateMetadata = async ({
  params,
}: {
  params: Promise<{ city: string }>;
}): Promise<Metadata> => {
  const { city: cityNameParam } = await params;

  const citiesRes = await fetchData<CitiesQuery, CitiesQueryVariables>(
    CitiesDocument,
    {
      citiesInput: {
        slug: cityNameParam,
      },
    }
  )();
  const city = citiesRes?.cities?.docs?.[0];
  const coverImage = city?.coverImage;

  return {
    title: `Find the Best Bars with Happy Hours in ${city.name} - Seeker Social`,
    description: `Discover all kinds of bars with great happy hour deals near you, perfect for students, afterwork drinks, or nights out with friends. Cheap cocktails, beers, and the best terraces in ${city.name}`,
    openGraph: {
      title: `Find the Best Bars with Happy Hours in ${city.name} - Seeker Social`,
      description: `Discover all kinds of bars with great happy hour deals near you, perfect for students, afterwork drinks, or nights out with friends. Cheap cocktails, beers, and the best terraces in ${city.name}`,
      url: `https://seeker.com/${city.slug}`,
      images: [
        {
          url: coverImage ?? "",
          width: 1200,
          height: 630,
          alt: `Bars in ${city.name}`,
        },
      ],
    },
  };
};

export default async function CityPage({
  params,
}: {
  params: Promise<{ city: string }>;
}) {
  try {
    const cityName = (await params).city;

    const cityRes = await fetchData<CitiesQuery, CitiesQueryVariables>(
      CitiesDocument,
      {
        citiesInput: {
          slug: cityName,
        },
      }
    )();

    const citiesRes = await fetchData<CitiesQuery, CitiesQueryVariables>(
      CitiesDocument
    )();

    if (!citiesRes?.cities?.docs?.[0]) {
      throw new Error("We couldn't find the page you are looking for...");
    }

    if (!cityRes?.cities?.docs?.[0]) {
      throw new Error("We couldn't find the page you are looking for...");
    }

    const city = cityRes.cities.docs[0];

    const jsonLd = {
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      itemListElement: [
        {
          "@type": "ListItem",
          position: 1,
          name: "Home",
          item: "https://seeker.social",
        },
        {
          "@type": "ListItem",
          position: 2,
          name: cityName,
          item: `https://seeker.social/${cityName}`,
        },
      ],
    };

    const barsRes = await fetchData<BarsQuery, BarsQueryVariables>(
      BarsDocument,
      {
        barsInput: { city: city._id, featured: true },
        paginationInput: { limit: 10 },
      }
    )();

    if (!barsRes?.bars) {
      throw new Error("No bars found");
    }

    const clubsRes = await fetchData<ClubsQuery, ClubsQueryVariables>(
      ClubsDocument,
      {
        clubsInput: { city: city._id, featured: true },
        paginationInput: { limit: 10 },
      }
    )();

    if (!clubsRes?.clubs) {
      throw new Error("No clubs found");
    }

    return (
      <div>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(jsonLd).replace(/</g, "\\u003c"),
          }}
        />
        {city?.coverImage ? <LandingPage.HeroSection {...city} /> : null}
        <LandingPage.PopularSections
          cities={citiesRes.cities.docs}
          bars={barsRes.bars.docs}
          clubs={clubsRes.clubs.docs}
        />
        <LandingPage.CallToDownloadSection />
      </div>
    );
  } catch (error) {
    return <NotFound error={error as Error} />;
  }
}
