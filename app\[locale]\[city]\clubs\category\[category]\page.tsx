import { softwareApplicationLd } from "@/app/[city]/bars/page";
import NotFound from "@/app/not-found";
import { fetchData } from "@/client";
import ClubCard from "@/components/cards/club.card";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import ClubsHomePage from "@/elements/clubs-homepage";
import {
  CitiesDocument,
  CitiesQuery,
  CitiesQueryVariables,
  ClubCategoriesDocument,
  ClubCategoriesQuery,
  ClubCategoriesQueryVariables,
  ClubCategoryByNameDocument,
  ClubCategoryByNameQuery,
  ClubCategoryByNameQueryVariables,
  ClubsDocument,
  ClubsQuery,
  ClubsQueryVariables,
} from "@/generated/graphql";
import type { Metadata } from "next";
import Link from "next/link";

export const revalidate = 3600;

export async function generateStaticParams() {
  // fetch all cities
  const citiesRes = await fetchData<CitiesQuery, CitiesQueryVariables>(
    CitiesDocument
  )();
  const cities = citiesRes?.cities?.docs || [];

  // fetch all categories
  const categoriesRes = await fetchData<
    ClubCategoriesQuery,
    ClubCategoriesQueryVariables
  >(ClubCategoriesDocument)();
  const categories = categoriesRes?.clubCategories?.docs || [];

  // return all possible city/category combinations
  return cities.flatMap((city) =>
    categories.map((category) => ({
      city: city.slug,
      category: category.slug,
    }))
  );
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ city: string; category: string }>;
}): Promise<Metadata> {
  const { city: cityParam, category: categoryParam } = await params;

  const selectedCityRes = await fetchData<CitiesQuery, CitiesQueryVariables>(
    CitiesDocument,
    { citiesInput: { slug: cityParam } }
  )();

  const cityName = selectedCityRes?.cities?.docs[0].name;

  // fetch category for description
  const categoryRes = await fetchData<
    ClubCategoryByNameQuery,
    ClubCategoryByNameQueryVariables
  >(ClubCategoryByNameDocument, {
    slug: categoryParam,
  })();
  const categoryName = categoryRes?.clubCategory?.name;

  const description =
    categoryRes?.clubCategory?.description ||
    `Discover the best ${categoryName} clubs in ${cityName} with Seeker Social.`;

  return {
    title: `${categoryName} Clubs in ${cityName} | Seeker Social`,
    description,
    openGraph: {
      title: `${categoryName} Clubs in ${cityName} | Seeker Social`,
      description,
      url: `https://seeker.social/${cityParam}/clubs/category/${categoryParam}`,
      siteName: "Seeker Social",
      type: "website",
    },
  };
}

function getClubJsonLd(club: ClubsQuery["clubs"]["docs"][number]) {
  return {
    "@context": "https://schema.org",
    "@type": "NightClub",
    name: club.name,
    image: club.coverImage,
    url: `https://www.seeker.social/${club.city.slug}/clubs/${club.slug}`,
    description: club.description,
    address: {
      "@type": "PostalAddress",
      streetAddress: club.address?.address,
      addressLocality: club.city.name,
    },
    geo: {
      "@type": "GeoCoordinates",
      latitude: club.address?.location.center[1],
      longitude: club.address?.location.center[0],
    },
    telephone: `${club.contact?.countryCode}${club.contact?.phone}`,
    openingHours: ["Mo-Su 17:00-02:00"],
    servesCuisine: club.menu?.sections.map((section) => section.name),
  };
}

export default async function CategoryPage({
  params,
  searchParams,
}: {
  params: Promise<{ city: string; category: string }>;
  searchParams: Promise<{ page: string }>;
}) {
  const { category: categoryParam, city: cityParam } = await params;
  const { page } = await searchParams;
  const selectedCityRes = await fetchData<CitiesQuery, CitiesQueryVariables>(
    CitiesDocument,
    { citiesInput: { slug: cityParam } }
  )();
  const city = selectedCityRes?.cities?.docs[0];

  const currentPage = Number(page) || 1;

  // Fetch bar categories with error handling
  const clubCategoriesResponse = await fetchData<
    ClubCategoriesQuery,
    ClubCategoriesQueryVariables
  >(ClubCategoriesDocument)();

  if (!clubCategoriesResponse?.clubCategories) {
    return (
      <NotFound
        error={
          new Error("Something went wrong while fetching the bar categories...")
        }
      />
    );
  }

  const { clubCategories } = clubCategoriesResponse;

  const categoryRes = await fetchData<
    ClubCategoryByNameQuery,
    ClubCategoryByNameQueryVariables
  >(ClubCategoryByNameDocument, {
    slug: categoryParam,
  })();

  const categoryName = categoryRes?.clubCategory?.name;

  if (!categoryRes?.clubCategory) {
    return (
      <NotFound
        error={new Error("We couldn't find the page you are looking for...")}
      />
    );
  }
  const clubsResponse = await fetchData<ClubsQuery, ClubsQueryVariables>(
    ClubsDocument,
    {
      clubsInput: {
        city: city?._id,
        categories: categoryRes?.clubCategory?.id
          ? [categoryRes.clubCategory.id]
          : undefined,
      },
      paginationInput: { limit: 8, page: currentPage },
    }
  )();

  // Check if bars response is valid
  if (!clubsResponse?.clubs) {
    return <NotFound error={new Error("No bars found")} />;
  }

  const { clubs: clubsWithFilter } = clubsResponse;

  if (clubsWithFilter.docs === undefined) {
    return <NotFound error={new Error("No bars found")} />;
  }

  return (
    <div>
      <ClubsHomePage.HeroSection
        category={categoryName}
        description={categoryRes?.clubCategory?.description}
      />
      <script
        key="software-jsonld"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(softwareApplicationLd).replace(
            /</g,
            "\\u003c"
          ),
        }}
      />

      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(
            {
              "@context": "https://schema.org",
              "@type": "ItemList",
              itemListElement: clubsWithFilter.docs.map((schema, index) => ({
                "@type": "ListItem",
                position: index + 1,
                item: getClubJsonLd(schema),
              })),
            },
            null,
            2
          ),
        }}
      />
      <section id="categoryList" className="container">
        <div className="flex flex-row flex-wrap items-center justify-center gap-5 my-[65px] lg:my-[76px] ">
          {clubsWithFilter?.docs?.map((club) => (
            <Link
              href={`/${city.slug}/clubs/${club.slug}`}
              key={club.id}
              className="w-[298px]"
            >
              <ClubCard data={club} wrapperClassName="w-[298px]" />
            </Link>
          ))}
          <div className="w-full mt-2 lg:mt-6">
            <Pagination>
              <PaginationContent>
                {/* Previous Page */}
                {clubsWithFilter.hasPrevPage && (
                  <PaginationItem>
                    <PaginationPrevious
                      href={`?page=${clubsWithFilter.prevPage}`}
                    />
                  </PaginationItem>
                )}

                {/* First Page */}
                <PaginationItem>
                  <PaginationLink href={`?page=1`} isActive={currentPage === 1}>
                    1
                  </PaginationLink>
                </PaginationItem>

                {/* Second Page */}
                {clubsWithFilter.totalPages > 1 && (
                  <PaginationItem>
                    <PaginationLink
                      href={`?page=2`}
                      isActive={currentPage === 2}
                    >
                      2
                    </PaginationLink>
                  </PaginationItem>
                )}

                {/* Ellipsis if currentPage is far from beginning */}
                {currentPage > 4 && (
                  <PaginationItem>
                    <PaginationEllipsis />
                  </PaginationItem>
                )}

                {/* Current Page (if not 1 or 2 or last 2) */}
                {currentPage > 2 &&
                  currentPage < clubsWithFilter.totalPages - 1 && (
                    <PaginationItem>
                      <PaginationLink href={`?page=${currentPage}`} isActive>
                        {currentPage}
                      </PaginationLink>
                    </PaginationItem>
                  )}

                {/* Ellipsis before last 2 pages */}
                {currentPage < clubsWithFilter.totalPages - 3 && (
                  <PaginationItem>
                    <PaginationEllipsis />
                  </PaginationItem>
                )}

                {/* Second Last Page */}
                {clubsWithFilter.totalPages > 3 && (
                  <PaginationItem>
                    <PaginationLink
                      href={`?page=${clubsWithFilter.totalPages - 1}`}
                      isActive={currentPage === clubsWithFilter.totalPages - 1}
                    >
                      {clubsWithFilter.totalPages - 1}
                    </PaginationLink>
                  </PaginationItem>
                )}

                {/* Last Page */}
                {clubsWithFilter.totalPages > 2 && (
                  <PaginationItem>
                    <PaginationLink
                      href={`?page=${clubsWithFilter.totalPages}`}
                      isActive={currentPage === clubsWithFilter.totalPages}
                    >
                      {clubsWithFilter.totalPages}
                    </PaginationLink>
                  </PaginationItem>
                )}

                {/* Next Page */}
                {clubsWithFilter.hasNextPage && (
                  <PaginationItem>
                    <PaginationNext
                      href={`?page=${clubsWithFilter.nextPage}`}
                    />
                  </PaginationItem>
                )}
              </PaginationContent>
            </Pagination>
          </div>
        </div>
        <div className="flex flex-col gap-4 lg:gap-[28px] container">
          <div className="font-bold text-2xl leading-5 text-secondary">
            Explore Other Bars
          </div>
          <div className="flex flex-row items-center flex-wrap gap-2 sm:gap- mb-[72px]">
            {clubCategories?.docs
              ?.filter((category) => category.name !== categoryName)
              .map((category) => (
                <Link
                  key={category.id}
                  href={`/${city.slug}/clubs/category/${category.slug}?page=1`}
                  className="px-3 py-1.5 text-sm rounded-none sm:px-[28px] lg:text-xl font-bold leading-5 bg-primary text-white hover:text-background/80 transition-all duration-100 ease-in-out whitespace-nowrap"
                >
                  {category.name}
                </Link>
              ))}
          </div>
        </div>
      </section>
    </div>
  );
}
