import { fetchData } from "@/client";
import BarCard from "@/components/cards/bar.card";
import CityCard from "@/components/cards/city.card";
import ClubCard from "@/components/cards/club.card";
import HorizontalScrollCards from "@/components/common/HorizontalScrollCards";
import {
  BarsQuery,
  CitiesQuery,
  CityStatsDocument,
  CityStatsQuery,
  CityStatsQueryVariables,
  ClubsQuery,
} from "@/generated/graphql";
import Image from "next/image";
import Link from "next/link";

export const revalidate = 3600;

type PopularSectionProps = {
  cities: CitiesQuery["cities"]["docs"];
  bars: BarsQuery["bars"]["docs"];
  clubs: ClubsQuery["clubs"]["docs"];
};

export default async function PopularSections({
  cities,
  bars,
  clubs,
}: PopularSectionProps) {
  const citiesData = cities;
  const barsData = bars;
  const clubsData = clubs;
  const showClubs = clubsData?.filter((club) => club.featured).length !== 0;
  const showBars = barsData?.filter((bar) => bar.featured).length !== 0;
  const totalVenues = await Promise.all(
    citiesData.map(async ({ _id: cityId, coverImage, name }) => {
      const res = await fetchData<CityStatsQuery, CityStatsQueryVariables>(
        CityStatsDocument,
        {
          cityId,
        }
      )();
      return {
        barsCount: res.bars.totalDocs,
        clubsCount: res.clubs.totalDocs,
        cityImage: coverImage,
        cityName: name,
      };
    })
  );

  if (!totalVenues) {
    throw new Error("City data not found");
  }

  return (
    <div className="w-full bg-background">
      {/* POPULAR CITIES */}
      <section
        id="explore"
        className="w-full bg-background flex flex-col items-center container md:pt-[102px] pt-[43px] md:pb-[95px] pb-[38px] scroll-mt-[160px]"
      >
        <h2 className="font-bold text-xl mb-[59.5px] md:mb-[67px] md:text-4xl lg:text-[40px] selection:bg-primary selection:text-dark">
          Popular Cities
        </h2>
        <HorizontalScrollCards>
          {totalVenues?.map((city) => {
            if (!city) return null;

            return (
              <Link
                href={`/${city.cityName.toLowerCase()}/bars`}
                key={city.cityName}
              >
                <CityCard
                  cityImage={city.cityImage}
                  cityName={city.cityName}
                  totalBars={city.barsCount}
                  totalClubs={city.clubsCount}
                />
              </Link>
            );
          })}
        </HorizontalScrollCards>
      </section>

      {/* CHEERS SECTION */}
      <section className="w-full bg-gradient-to-br lg:bg-gradient-to-r from-primary  via-40% via-muted-primary to-90% to-secondary mt-[73.5px] md:mt-[95px]">
        <div className="w-full flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4 py-[52px] px-4 lg:py-16 container">
          <p className="font-medium text-[32px] md:text-[38px] selection:bg-primary selection:text-white xl:text-[48px] leading-[39px] md:leading-[50px] text-white md:max-w-[359px] lg:max-w-[450px] xl:max-w-[556px] xl:leading-[61px]">
            Find bars, clubs, student hotspots, and VIP venues tailored to your
            vibe.
          </p>
          <Image
            src={"/svg/cheers-white.svg"}
            width={320}
            height={117}
            alt="cheers"
            className="lg:hidden mt-4 self-start md:self-end w-[320px] h-[117px]"
          />
          <Image
            src={"/svg/cheers.svg"}
            width={380}
            height={128}
            alt="cheers"
            className="hidden w-[380px] h-[128px] lg:block "
          />
        </div>
      </section>

      {/* POPULAR BARS */}
      {showBars && (
        <section className="w-full bg-background flex flex-col items-center container md:mt-[111px] mt-[43px]">
          <h2 className="font-bold text-xl mb-[48px] md:mb-[75px] md:text-4xl lg:text-[40px] selection:bg-primary selection:text-dark">
            Popular Bars
          </h2>
          <div className="flex flex-col gap-[47px] sm:gap-0 w-full">
            <HorizontalScrollCards>
              {barsData?.map((card) => (
                <Link
                  href={`/${card.city.name.toLowerCase()}/bars/${card.slug}`}
                  key={card._id}
                >
                  <BarCard data={card} wrapperClassName="w-[298px]" />
                </Link>
              ))}
            </HorizontalScrollCards>
          </div>
        </section>
      )}

      {/* SOCIAL GATHERING  */}
      <section className="mt-[59px] lg:mt-[94px]">
        <div className="w-full h-[465px] lg:h-[594px] relative">
          <Image
            src={"/images/socialgathering.webp"}
            alt="socialgathering"
            fill
            className="hidden lg:block object-cover"
            loading="eager"
            sizes="(max-width: 1024px) 100vw, 100vw"
          />
          <Image
            src={"/images/socialgathering_mobile.webp"}
            alt="socialgathering"
            fill
            className="lg:hidden object-cover"
            loading="eager"
            sizes="(max-width: 768px) 100vw"
          />
          <Image
            src={"/svg/papercuts.svg"}
            width={407}
            height={53}
            alt="papercuts"
            className="hidden sm:block absolute bottom-0 w-full h-auto lg:w-full lg:h-auto object-contain"
          />
          <Image
            src={"/svg/papercuts_mobile.svg"}
            width={407}
            height={53}
            alt="papercuts"
            className="absolute bottom-0 w-full h-auto sm:hidden object-contain"
          />
        </div>
      </section>

      {/* POPULAR CLUBS */}
      {showClubs && (
        <section className="w-full bg-background flex flex-col items-center container md:mt-[102px] mt-[72px] ">
          <h2 className="font-bold text-xl mb-[47px] md:mb-[88px] md:text-4xl lg:text-[40px] selection:bg-primary selection:text-dark">
            Popular Clubs
          </h2>
          <div className="flex flex-col gap-[47px] sm:gap-0 w-full mb-[55px] md:mb-[75px]">
            <HorizontalScrollCards>
              {clubsData?.map((card) => (
                <Link
                  href={`/${card.city.name.toLowerCase()}/clubs/${card.slug}`}
                  key={card._id}
                >
                  <ClubCard data={card} wrapperClassName="w-[298px]" />
                </Link>
              ))}
            </HorizontalScrollCards>
          </div>
        </section>
      )}
    </div>
  );
}
