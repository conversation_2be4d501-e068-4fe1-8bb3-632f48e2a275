import * as React from "react";

const MOBILE_MAX = 767;
const TABLET_MAX = 1023;

type DeviceType = "mobile" | "tablet" | "desktop";

export function useDeviceType(): DeviceType {
  const [deviceType, setDeviceType] = React.useState<DeviceType>(() => {
    if (typeof window === "undefined") return "desktop"; // Default for SSR
    const width = window.innerWidth;
    if (width <= MOBILE_MAX) return "mobile";
    if (width <= TABLET_MAX) return "tablet";
    return "desktop";
  });

  React.useEffect(() => {
    const onResize = () => {
      const width = window.innerWidth;
      if (width <= MOBILE_MAX) setDeviceType("mobile");
      else if (width <= TABLET_MAX) setDeviceType("tablet");
      else setDeviceType("desktop");
    };

    window.addEventListener("resize", onResize);
    onResize(); // Call once on mount

    return () => window.removeEventListener("resize", onResize);
  }, []);

  return deviceType;
}
