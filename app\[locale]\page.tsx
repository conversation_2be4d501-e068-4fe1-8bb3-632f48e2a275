import { fetchData } from "@/client";
import LandingPage from "@/elements/landing-page";
import {
  BarsDocument,
  BarsQuery,
  BarsQueryVariables,
  CitiesDocument,
  CitiesQuery,
  CitiesQueryVariables,
  ClubsDocument,
  ClubsQuery,
  ClubsQueryVariables,
} from "@/generated/graphql";
import NotFound from "./not-found";

export const revalidate = 3600;

//ORGANIZATION-SCHEMA
const organizationLd = {
  "@context": "https://schema.org",
  "@type": "Organization",
  name: "Seeker Social",
  url: "https://seeker.social",
  logo: "https://seeker.social/logo.svg",
  description:
    "Seeker Social is a nightlife discovery platform helping users find happy hour bars and clubs in France. Users can filter by city, category, and book directly from the app.",
  sameAs: [
    "https://instagram.com/seeker.social",
    "https://www.tiktok.com/@seeker.social",
  ],
  contactPoint: {
    "@type": "ContactPoint",
    contactType: "customer support",
    email: "<EMAIL>",
    availableLanguage: ["English", "French"],
  },
};

export const metadata = {
  title: `Find the Best Bars with Happy Hours in Paris - Seeker Social`,
  description:
    "Discover all kinds of bars with great happy hour deals near you, perfect for students, afterwork drinks, or nights out with friends. Cheap cocktails, beers, and the best terraces in town",
};

export default async function LandingPageScreen(
  params: Promise<{ locale: string }>
) {
  const { locale } = await params;
  try {
    const jsonLd = {
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      itemListElement: [
        {
          "@type": "ListItem",
          position: 1,
          name: "Home",
          item: `https://seeker.social/${locale}`,
        },
      ],
    };

    // Fetch city data with error handling
    const cityRes = await fetchData<CitiesQuery, CitiesQueryVariables>(
      CitiesDocument,
      { citiesInput: { slug: "paris" } }
    )();
    const citiesRes = await fetchData<CitiesQuery, CitiesQueryVariables>(
      CitiesDocument
    )();

    if (!citiesRes?.cities?.docs?.[0]) {
      throw new Error("We couldn't find the page you are looking for...");
    }

    if (!cityRes?.cities?.docs?.[0]) {
      throw new Error("We couldn't find the page you are looking for...");
    }

    const city = cityRes.cities.docs[0];

    // Fetch bars with error handling
    const barsRes = await fetchData<BarsQuery, BarsQueryVariables>(
      BarsDocument,
      {
        barsInput: { city: city._id, featured: true },
        paginationInput: { limit: 10 },
      }
    )();

    if (!barsRes?.bars) {
      throw new Error("No bars found");
    }

    const bars = barsRes.bars;

    // Fetch clubs with error handling
    const clubsRes = await fetchData<ClubsQuery, ClubsQueryVariables>(
      ClubsDocument,
      {
        clubsInput: { city: city._id, featured: true },
        paginationInput: { limit: 10 },
      }
    )();

    if (!clubsRes?.clubs) {
      throw new Error("No clubs found");
    }

    const clubs = clubsRes.clubs;
    const cities = citiesRes.cities.docs;

    return (
      <div>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(jsonLd).replace(/</g, "\\u003c"),
          }}
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(organizationLd).replace(/</g, "\\u003c"),
          }}
        />

        {city?.coverImage ? <LandingPage.HeroSection {...city} /> : null}
        <LandingPage.PopularSections
          cities={cities}
          bars={bars?.docs}
          clubs={clubs?.docs}
        />
        <LandingPage.CallToDownloadSection />
      </div>
    );
  } catch (error) {
    return <NotFound error={error as Error} />;
  }
}
