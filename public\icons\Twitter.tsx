import { IconProps } from "@/types";
import * as React from "react";
const TwitterIcon = ({ className, color, height, width }: IconProps) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 18 15"
    fill="none"
    className={className}
  >
    <path
      d="M15.9402 3.76196C15.9402 3.92798 15.9402 4.06079 15.9402 4.22681C15.9402 8.84204 12.4539 14.1213 6.0457 14.1213C4.05352 14.1213 2.22734 13.5569 0.700001 12.5608C0.965626 12.594 1.23125 12.6272 1.53008 12.6272C3.15703 12.6272 4.65117 12.0627 5.84649 11.1331C4.31914 11.0999 3.02422 10.1038 2.59258 8.70923C2.825 8.74243 3.02422 8.77563 3.25664 8.77563C3.55547 8.77563 3.8875 8.70923 4.15313 8.64282C2.55938 8.31079 1.36406 6.91626 1.36406 5.2229V5.1897C1.82891 5.45532 2.39336 5.58813 2.95781 5.62134C1.99492 4.99048 1.39727 3.92798 1.39727 2.73267C1.39727 2.0686 1.56328 1.47095 1.86211 0.9729C3.58867 3.0647 6.17852 4.45923 9.06719 4.62524C9.00078 4.35962 8.96758 4.09399 8.96758 3.82837C8.96758 1.90259 10.5281 0.342041 12.4539 0.342041C13.45 0.342041 14.3465 0.740479 15.0105 1.43774C15.7742 1.27173 16.5379 0.9729 17.202 0.574463C16.9363 1.40454 16.4051 2.0686 15.6746 2.50024C16.3719 2.43384 17.0691 2.23462 17.6668 1.96899C17.202 2.66626 16.6043 3.26392 15.9402 3.76196Z"
      fill={color}
    />
  </svg>
);
export default TwitterIcon;
