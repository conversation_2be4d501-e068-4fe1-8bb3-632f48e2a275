"use client";

import AccordionIcons from "@/components/common/AccordionIcons";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { ClubsQuery } from "@/generated/graphql";
import { useEffect, useRef, useState } from "react";

type FAQProps = {
  faqDetails: ClubsQuery["clubs"]["docs"][0]["faqs"];
};

export default function FAQSection({ faqDetails }: FAQProps) {
  const lastFAQRef = useRef<HTMLDivElement | null>(null);
  const containerRef = useRef<HTMLDivElement | null>(null);

  const [openItem, setOpenItem] = useState<string>("");

  // Fixed logic for last FAQ scrolling
  useEffect(() => {
    const lastIndex = faqDetails?.length ? faqDetails.length - 1 : -1;
    if (
      openItem === `FAQ-${lastIndex}` &&
      lastFAQRef.current &&
      containerRef.current
    ) {
      const container = containerRef.current;
      const item = lastFAQRef.current;

      // Small delay to ensure accordion is fully expanded
      setTimeout(() => {
        container.scrollTo({
          top: item.offsetTop - container.offsetTop,
          behavior: "smooth",
        });
      }, 150);
    }
  }, [openItem, faqDetails?.length]);

  if (!faqDetails?.length) {
    return null;
  }

  return (
    <section className="w-full h-[630px] flex flex-col items-center container pb-[50px]">
      <h2 className="text-xl lg:text-3xl xl:text-[40px] font-bold text-text mt-3 xl:mt-4 text-center">
        Frequently Asked Questions
      </h2>
      <div
        ref={containerRef}
        className="mt-[42px] overflow-y-auto scrollbar-theme w-full max-w-[1079px] flex flex-col gap-2"
      >
        <Accordion
          type="single"
          collapsible
          value={openItem}
          onValueChange={(val) => setOpenItem(val || "")}
        >
          {faqDetails.map((faq, idx) => (
            <AccordionItem
              //@ts-ignore
              key={faq.id || idx} // Use unique ID if available
              value={`FAQ-${idx}`}
              ref={idx === faqDetails.length - 1 ? lastFAQRef : null}
            >
              <AccordionTrigger
                className="text-sm lg:text-base bg-[#FFF9EC] px-4 py-6 text-text"
                chevron={false}
              >
                <div className="flex items-center gap-2 w-full">
                  <AccordionIcons />
                  <div className="text-left font-montserrat-alternates text-textgreen font-semibold ml-2">
                    {faq.title}
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent className="text-sm lg:text-base px-6 lg:px-[59px] py-5 font-montserrat-alternates font-semibold">
                {faq.content}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </section>
  );
}
