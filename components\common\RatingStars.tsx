import { cn } from "@/lib/utils";
import Image from "next/image";
import React from "react";

const star = "/svg/empty-star.svg";
const halfStar = "/svg/half-star.svg";
const fillStar = "/svg/fill-star.svg";

interface RatingStarsProps {
  ratingValue?: number;
  className?: string;
  orientation?: "horizontal" | "vertical";
  width?: number;
  height?: number;
  showRating?: boolean;
  textStyles?: string;
  starGap?: string;
}

export default function RatingStars({
  className,
  ratingValue = 3,
  orientation = "horizontal",
  width = 16,
  height = 16,
  showRating = true,
  textStyles,
  starGap = "gap-1",
}: RatingStarsProps) {
  const normalizedRating = Math.max(0, Math.min(5, ratingValue || 0));
  const maxStars = 5;
  const fullStars = Math.floor(normalizedRating);
  const remainder = normalizedRating - fullStars;

  const hasHalfStar = remainder >= 0.25 && remainder < 0.75;
  const hasThreeQuarterStar = remainder >= 0.75;

  const actualFullStars = hasThreeQuarterStar ? fullStars + 1 : fullStars;
  const actualHalfStar = hasThreeQuarterStar ? false : hasHalfStar;
  const emptyStars = maxStars - actualFullStars - (actualHalfStar ? 1 : 0);

  const containerClasses = cn(
    "flex items-center",
    orientation === "vertical" ? "flex-col" : "flex-row",
    className
  );

  const starsContainerClasses = cn("flex items-center", starGap);
  const textClasses = cn(
    "ml-2",
    textStyles || "text-sm text-gray-600 font-medium"
  );

  return (
    <div className={containerClasses}>
      <div className={starsContainerClasses}>
        {Array.from({ length: actualFullStars }).map((_, index) => (
          <Image
            key={`full-${index}`}
            src={fillStar}
            alt={`Full star ${index + 1}`}
            width={width}
            height={height}
          />
        ))}
        {actualHalfStar && (
          <Image
            key="half-star"
            src={halfStar}
            alt="Half star"
            width={width}
            height={height}
          />
        )}
        {Array.from({ length: emptyStars }).map((_, index) => (
          <Image
            key={`empty-${index}`}
            src={star}
            alt={`Empty star ${
              actualFullStars + (actualHalfStar ? 1 : 0) + index + 1
            }`}
            width={width}
            height={height}
          />
        ))}
        {showRating && (
          <span className={textClasses}>({normalizedRating.toFixed(1)})</span>
        )}
      </div>
    </div>
  );
}
