query Bars($barsInput: BarsInput, $paginationInput: PaginationInput) {
  bars(barsInput: $barsInput, paginationInput: $paginationInput) {
    totalDocs
    limit
    page
    totalPages
    prevPage
    nextPage
    hasPrevPage
    hasNextPage
    pagingCounter
    docs {
      _id
      id
      createdAt
      updatedAt
      slug
      name
      description
      status
      logo
      coverImage
      images
      featured
      rating
      address {
        address
        metroLine
        metroStation
        location {
          center
        }
      }
      categories {
        name
        id
      }
      tags {
        id
        name
      }
      city {
        id
        name
        slug
      }
      businessHours {
        schedule {
          day
          timings
        }
      }
      happyHours {
        schedule {
          day
          timings
        }
      }
      contact {
        countryCode
        phone
      }
      faqs {
        content
        title
      }
      seo {
        metaTitle
        metaDescription
        canonicalUrl
        ogTitle
        ogDescription
        seoKeywords
      }
      menu {
        currency
        startingPrice
        happyHourStartingPrice
        sections {
          name
          items {
            name
            price
            available
            happyHourPrice
          }
        }
      }
    }
  }
}

query BarCategories($paginationInput: PaginationInput) {
  barCategories(paginationInput: $paginationInput) {
    totalDocs
    limit
    page
    totalPages
    prevPage
    nextPage
    hasPrevPage
    hasNextPage
    pagingCounter
    docs {
      _id
      id
      createdAt
      updatedAt
      name
      description
      image
      icon
      slug
    }
  }
}

query BarCategoryByName($slug: String) {
  barCategory(id: null, slug: $slug) {
    _id
    id
    createdAt
    updatedAt
    name
    description
    image
    icon
    slug
  }
}
query BarCategoryByID($id: ID!) {
  barCategory(id: $id) {
    _id
    id
    createdAt
    updatedAt
    name
    description
    image
    icon
    slug
  }
}
