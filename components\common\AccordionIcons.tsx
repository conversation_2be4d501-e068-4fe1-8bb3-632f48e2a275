import COLORS from "@/constants/colors";
import ICONS from "@/public/icons";

const AcordionIcons = () => {
  return (
    <div className="flex items-center gap-2">
      {/* Minus Icon: Only visible when open */}
      <span data-state="open" className="hidden group-data-[state=open]:block">
        <ICONS.MinusIcon
          width={18}
          height={7}
          color={COLORS.LIGHT.PRIMARY}
          className="lg:w-[18px] lg:h-[7px]"
        />
      </span>

      {/* Plus Icon: Only visible when closed */}

      <span
        data-state="closed"
        className="block group-data-[state=open]:hidden"
      >
        <ICONS.PlusIcon
          width={18}
          height={18}
          color={COLORS.LIGHT.PRIMARY}
          className="lg:w-[18px] lg:h-[18px]"
        />
      </span>
    </div>
  );
};
export default AcordionIcons;
