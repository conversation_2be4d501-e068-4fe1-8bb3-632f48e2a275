import { IconProps } from "@/types";
import * as React from "react";
const PintrestIcon = ({ className, color, height, width }: IconProps) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 14 18"
    fill="none"
    className={className}
  >
    <path
      d="M7.57773 0.94751C10.6988 0.94751 13.5543 3.10571 13.5543 6.39282C13.5543 9.48071 11.9605 12.9338 8.44102 12.9338C7.57773 12.9338 6.54844 12.5022 6.1168 11.7385C5.38633 14.7268 5.41953 15.1917 3.75938 17.4827C3.59336 17.5491 3.62656 17.5491 3.52695 17.4163C3.46055 16.7854 3.39414 16.1877 3.39414 15.5569C3.39414 13.5315 4.32383 10.5764 4.78867 8.61743C4.52305 8.08618 4.45664 7.48853 4.45664 6.92407C4.45664 4.26782 7.57773 3.86938 7.57773 6.06079C7.57773 7.35571 6.68125 8.58423 6.68125 9.84595C6.68125 10.676 7.41172 11.2737 8.2418 11.2737C10.5328 11.2737 11.2301 7.98657 11.2301 6.22681C11.2301 3.86938 9.56992 2.57446 7.27891 2.57446C4.65586 2.57446 2.63047 4.46704 2.63047 7.12329C2.63047 8.41821 3.42734 9.08228 3.42734 9.3811C3.42734 9.64673 3.22813 10.5432 2.89609 10.5432C2.09922 10.5432 0.804297 9.21509 0.804297 6.89087C0.804297 3.20532 4.15781 0.94751 7.57773 0.94751Z"
      fill={color}
    />
  </svg>
);
export default PintrestIcon;
