import { fetchData } from "@/client";
import Footer from "@/components/common/Footer";
import Navbar from "@/components/common/Navbar";
import {
  CitiesDocument,
  CitiesQuery,
  CitiesQueryVariables,
} from "@/generated/graphql";
import { routing } from "@/i18n/routing";
import type { Metadata } from "next";
import { hasLocale, NextIntlClientProvider } from "next-intl";
import localFont from "next/font/local";
import "./globals.css";
import NotFound from "./not-found";
import RootProvider from "@/providers/root.provider";

const montserrat_alternates = localFont({
  src: [
    {
      path: "./fonts/montserrat-alter/MontserratAlternates-Light.ttf",
      weight: "300",
    },
    {
      path: "./fonts/montserrat-alter/MontserratAlternates-Regular.ttf",
      weight: "400",
    },
    {
      path: "./fonts/montserrat-alter/MontserratAlternates-Medium.ttf",
      weight: "500",
    },
    {
      path: "./fonts/montserrat-alter/MontserratAlternates-SemiBold.ttf",
      weight: "600",
    },

    {
      path: "./fonts/montserrat-alter/MontserratAlternates-Bold.ttf",
      weight: "700",
    },
    {
      path: "./fonts/montserrat-alter/MontserratAlternates-ExtraBold.ttf",
      weight: "800",
    },
  ],
  variable: "--font-montserrat-alternates",
});

const montserrat = localFont({
  src: [
    {
      path: "./fonts/montserrat/Montserrat-Light.ttf",
      weight: "300",
    },
    {
      path: "./fonts/montserrat/Montserrat-Regular.ttf",
      weight: "400",
    },
    {
      path: "./fonts/montserrat/Montserrat-Medium.ttf",
      weight: "500",
    },
    {
      path: "./fonts/montserrat/Montserrat-SemiBold.ttf",
      weight: "600",
    },

    {
      path: "./fonts/montserrat/Montserrat-Bold.ttf",
      weight: "700",
    },
    {
      path: "./fonts/montserrat/Montserrat-ExtraBold.ttf",
      weight: "800",
    },
  ],
  variable: "--font-montserrat",
});

export const metadata: Metadata = {
  title: "Seeker",
  description:
    "Seeker is a platform that connects users with bars and clubs, offering a seamless way to explore venues across cities.",
  icons: {},
};

export default async function RootLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}>) {
  const { locale } = await params;

  if (!hasLocale(routing.locales, locale)) {
    return <NotFound error={new Error("Locale not found")} />;
  }

  try {
    const citiesQuery = await fetchData<CitiesQuery, CitiesQueryVariables>(
      CitiesDocument,
      {
        paginationInput: { limit: 6 },
      }
    )();
    if (!citiesQuery.cities.docs) {
      throw new Error("Cities not found");
    }

    return (
      <html lang={locale} className="scroll-smooth">
        <body
          className={`${montserrat_alternates.variable} ${montserrat.variable}  antialiased font-montserrat-alternates`}
        >
          <RootProvider>
            <NextIntlClientProvider>
              <Navbar {...citiesQuery.cities} />
              {children}
              <Footer {...citiesQuery.cities} />
            </NextIntlClientProvider>
          </RootProvider>
        </body>
      </html>
    );
  } catch (error) {
    return (
      <html lang={locale} className="scroll-smooth">
        <body>
          <NotFound error={error as Error} />
        </body>
      </html>
    );
  }
}
