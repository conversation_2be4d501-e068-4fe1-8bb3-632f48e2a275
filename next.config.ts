import type { NextConfig } from "next";
import createNextIntlPlugin from "next-intl/plugin";

//REMEMBER TO ADD SPECIFIC DOMAINS HERE

const nextConfig: NextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "**", // Accepts all hostnames (not recommended for production)
      },
    ],
  },
  async redirects() {
    return [
      {
        source: "/:lang(en|fr)/:path*",
        destination: "/:path*",
        permanent: true,
      },
    ];
  },
};

const withNextIntl = createNextIntlPlugin();
export default withNextIntl(nextConfig);
