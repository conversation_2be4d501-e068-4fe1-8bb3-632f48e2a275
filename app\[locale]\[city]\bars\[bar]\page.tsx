import NotFound from "@/app/not-found";
import { fetchData } from "@/client";
import BarDetail from "@/elements/bar-detail-page";
import {
  BarsDocument,
  BarsQuery,
  BarsQueryVariables,
} from "@/generated/graphql";
import { unslugify } from "@/lib/utils";
import { Metadata } from "next";

export const revalidate = 3600;

type GetJsonLdProps = {
  faqDetails: BarsQuery["bars"]["docs"][0]["faqs"];
};

export async function generateStaticParams() {
  const res = await fetchData<BarsQuery, BarsQueryVariables>(BarsDocument)();

  const bars = res?.bars?.docs ?? [];

  // Only return the actual route parameters
  return bars.map((bar) => ({
    bar: bar.slug,
  }));
}

export const generateMetadata = async (props: {
  params: Promise<{ bar: string }>;
}): Promise<Metadata> => {
  const { bar: slugParam } = await props.params;

  // Fetch the bar data to get the metadata
  try {
    const res = await fetchData<BarsQuery, BarsQueryVariables>(BarsDocument, {
      barsInput: { slug: slugParam },
      paginationInput: { limit: 1 },
    })();

    const bar = res?.bars?.docs?.[0];

    if (!bar) {
      return {
        title: "Bar Not Found | Seeker Social",
        description:
          "The requested bar could not be found. Discover other amazing bars and nightlife spots on Seeker Social.",
      };
    }

    // Fallback values for better SEO
    const title = bar.seo?.metaTitle || bar.name || "Bar Details";
    const description =
      bar.seo?.metaDescription ||
      bar.description ||
      `Discover ${bar.name} on Seeker Social`;
    const ogTitle = bar.seo?.ogTitle || title;
    const ogDescription = bar.seo?.ogDescription || description;
    const canonicalUrl = bar.seo?.canonicalUrl;

    const metadata: Metadata = {
      title,
      description,
      openGraph: {
        title: ogTitle,
        description: ogDescription,
        type: "website",
        siteName: "Seeker Social",
        locale: "en_US",
        ...(canonicalUrl && { url: canonicalUrl }),
        ...(bar.coverImage && {
          images: [
            {
              url: bar.coverImage,
              width: 1200,
              height: 630,
              alt: `${bar.name} cover image`,
            },
          ],
        }),
      },
      twitter: {
        card: "summary_large_image",
        title: ogTitle,
        description: ogDescription,
        ...(bar.coverImage && {
          images: [bar.coverImage],
        }),
      },
      // Add canonical URL if available
      ...(canonicalUrl && {
        alternates: {
          canonical: canonicalUrl,
        },
      }),
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          "max-video-preview": -1,
          "max-image-preview": "large",
          "max-snippet": -1,
        },
      },
    };

    return metadata;
  } catch (error) {
    console.log(error);
    return {
      title: "Bar Not Found | Seeker Social",
      description:
        "The requested bar could not be found. Discover other amazing bars and nightlife spots on Seeker Social.",
      robots: {
        index: false,
        follow: true,
      },
    };
  }
};

function generateMenuJsonLD(bar: BarsQuery["bars"]["docs"][number]) {
  if (!bar.menu?.sections || bar.menu.sections.length === 0) {
    return null;
  }
  const jsonLD = {
    "@context": "https://schema.org",
    "@type": "Menu",
    name: bar.name,
    url: `https://seeker.social/${bar.city.slug}/bars/${bar.slug}`,
    hasMenuSection: bar.menu?.sections.map((section) => ({
      "@type": "MenuSection",
      name: section.name,
      menuItem: section.items.map((item) => ({
        "@type": "MenuItem",
        name: item.name,
        offers: [
          {
            "@type": "Offer",
            price: item.price,
            priceCurrency: "EUR",
          },
          {
            "@type": "Offer",
            price: item.happyHourPrice,
            priceCurrency: "EUR",
            priceSpecification: "HAPPY_HOUR",
          },
        ],
      })),
    })),
  };

  return JSON.stringify(jsonLD, null, 2);
}

function generateJsonLd(bar: BarsQuery["bars"]["docs"][number]) {
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "BarOrPub",
    name: bar.name,
    image: bar.coverImage,
    url: `https://www.seeker.social/${bar.city.slug}/bars/${bar.slug}`,
    description: bar.description,
    address: {
      "@type": "PostalAddress",
      streetAddress: bar.address?.address,
      addressLocality: bar.city.name,
    },
    geo: {
      "@type": "GeoCoordinates",
      latitude: bar.address?.location.center[1],
      longitude: bar.address?.location.center[0],
    },
    telephone: `${bar.contact?.countryCode}${bar.contact?.phone}`,
    openingHours: ["Mo-Su 17:00-02:00"],
    servesCuisine: bar.menu?.sections.map((section) => section.name),
  };

  return JSON.stringify(jsonLd, null, 2);
}

function getFAQJsonLd({ faqDetails }: GetJsonLdProps) {
  if (!faqDetails || faqDetails.length === 0) {
    return null;
  }

  const faqSchema = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    mainEntity: faqDetails.map((faq) => ({
      "@type": "Question",
      name: faq.title,
      acceptedAnswer: {
        "@type": "Answer",
        text: faq.content,
      },
    })),
  };

  return JSON.stringify(faqSchema);
}

export default async function BarDetailPage({
  params,
}: {
  params: Promise<{ bar: string; city: string }>;
}) {
  const { bar: slugParam, city: cityParam } = await params;

  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: [
      {
        "@type": "ListItem",
        position: 1,
        name: "Home",
        item: "https://seeker.social",
      },
      {
        "@type": "ListItem",
        position: 2,
        name: cityParam,
        item: `https://seeker.social/${cityParam}`,
      },
      {
        "@type": "ListItem",
        position: 3,
        name: "Bars",
        item: `https://seeker.social/${cityParam}/bars`,
      },
      {
        "@type": "ListItem",
        position: 4,
        name: unslugify(slugParam),
        item: `https://seeker.social/${cityParam}/bars/${slugParam}`,
      },
    ],
  };

  try {
    const res = await fetchData<BarsQuery, BarsQueryVariables>(BarsDocument, {
      barsInput: { slug: slugParam },
      paginationInput: { limit: 1 },
    })();

    const bar = res.bars.docs[0];
    const { bars: otherBars } = await fetchData<BarsQuery, BarsQueryVariables>(
      BarsDocument,
      { barsInput: { city: bar.city.id, featured: true } }
    )();

    const menuJsonLD = generateMenuJsonLD(bar);
    const listingJsonLd = generateJsonLd(bar);
    const faqJsonLd = getFAQJsonLd({ faqDetails: bar.faqs });

    return (
      <div className="bg-backgroundlight sm:bg-transparent">
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(jsonLd).replace(/</g, "\\u003c"),
          }}
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: listingJsonLd.replace(/</g, "\\u003c"),
          }}
        />
        {menuJsonLD && (
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{
              __html: menuJsonLD.replace(/</g, "\\u003c"),
            }}
          />
        )}
        {faqJsonLd && (
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{
              __html: faqJsonLd.replace(/</g, "\\u003c"),
            }}
          />
        )}

        <BarDetail.BarDetailSection bar={bar} otherBars={otherBars} />
      </div>
    );
  } catch (error) {
    return <NotFound error={error as Error} />;
  }
}
