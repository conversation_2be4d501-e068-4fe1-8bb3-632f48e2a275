query Clubs($clubsInput: ClubsInput, $paginationInput: PaginationInput) {
  clubs(clubsInput: $clubsInput, paginationInput: $paginationInput) {
    totalDocs
    limit
    page
    totalPages
    prevPage
    nextPage
    hasPrevPage
    hasNextPage
    pagingCounter
    docs {
      _id
      id
      createdAt
      updatedAt
      slug
      name
      description
      status
      logo
      coverImage
      images
      featured
      rating
      address {
        address
        metroLine
        metroStation
        location {
          center
        }
      }
      categories {
        name
        id
      }
      city {
        id
        name
        slug
      }
      tags {
        id
        name
      }
      businessHours {
        schedule {
          day
          timings
        }
      }
      contact {
        countryCode
        phone
      }
      faqs {
        content
        title
      }
      seo {
        metaTitle
        metaDescription
        canonicalUrl
        ogTitle
        ogDescription
        seoKeywords
      }
      menu {
        currency
        startingPrice
        sections {
          name
          items {
            name
            price
            available
          }
        }
      }
    }
  }
}

query ClubCategories($paginationInput: PaginationInput) {
  clubCategories(paginationInput: $paginationInput) {
    totalDocs
    limit
    page
    totalPages
    prevPage
    nextPage
    hasPrevPage
    hasNextPage
    pagingCounter
    docs {
      _id
      id
      createdAt
      updatedAt
      name
      description
      image
      icon
      slug
    }
  }
}

query ClubCategoryByName($slug: String) {
  clubCategory(id: null, slug: $slug) {
    _id
    id
    createdAt
    updatedAt
    name
    description
    image
    icon
    slug
  }
}

query ClubCategoryByID($id: ID!) {
  clubCategory(id: $id) {
    _id
    id
    createdAt
    updatedAt
    name
    description
    image
    icon
    slug
  }
}
