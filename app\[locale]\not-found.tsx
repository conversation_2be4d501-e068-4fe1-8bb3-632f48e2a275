import Link from "next/link";

export default function NotFound({ error }: { error: Error }) {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-white text-secondary px-4">
      <h1 className=" text-4xl sm:text-6xl font-bold mb-4 text-center">
        {error instanceof Error
          ? error.message
          : "An unexpected error occurred"}
      </h1>
      <Link
        href="/"
        className="text-blue-600 hover:underline text-base font-medium hover:underline-offset-4"
      >
        Back to the homepage
      </Link>
    </div>
  );
}
