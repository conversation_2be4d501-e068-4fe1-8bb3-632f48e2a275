import NotFound from "@/app/not-found";
import { fetchData } from "@/client";
import ClubCard from "@/components/cards/club.card";
import GridCard from "@/components/cards/grid.card";
import HorizontalScrollCards from "@/components/common/HorizontalScrollCards";
import {
  CityDocument,
  CityQuery,
  CityQueryVariables,
  ClubCategoriesDocument,
  ClubCategoriesQuery,
  ClubCategoriesQueryVariables,
  ClubsDocument,
  ClubsQuery,
  ClubsQueryVariables,
} from "@/generated/graphql";
import { slugify } from "@/lib/utils";
import Link from "next/link";

export const revalidate = 3600;

type ClubsProps = {
  cityId: string;
};

//SchemaGenerator
function getClubJsonLd(club: ClubsQuery["clubs"]["docs"][number]) {
  return {
    "@context": "https://schema.org",
    "@type": "NightClub",
    name: club.name,
    image: club.coverImage,
    url: `https://www.seeker.social/${club.city.slug}/clubs/${club.slug}`,
    description: club.description,
    address: {
      "@type": "PostalAddress",
      streetAddress: club.address?.address,
      addressLocality: club.city.name,
    },
    geo: {
      "@type": "GeoCoordinates",
      latitude: club.address?.location.center[1],
      longitude: club.address?.location.center[0],
    },
    telephone: `${club.contact?.countryCode}${club.contact?.phone}`,
    openingHours: ["Mo-Su 17:00-02:00"],
    servesCuisine: club.menu?.sections.map((section) => section.name),
  };
}

export default async function ClubsSection({ cityId }: ClubsProps) {
  try {
    const { clubs: popularClubs } = await fetchData<
      ClubsQuery,
      ClubsQueryVariables
    >(ClubsDocument, {
      clubsInput: { city: cityId, featured: true },
      paginationInput: { limit: 6 },
    })();

    const city = await fetchData<CityQuery, CityQueryVariables>(CityDocument, {
      cityId,
    })();

    // Collect schema data here
    const allSchemas: object[] = [];

    const {
      clubCategories: { docs: clubCategories },
    } = await fetchData<ClubCategoriesQuery, ClubCategoriesQueryVariables>(
      ClubCategoriesDocument,
      { paginationInput: { limit: 5 } }
    )();

    const categorySections = await Promise.all(
      clubCategories.map(async (c) => {
        const { clubs: clubsWithFilter } = await fetchData<
          ClubsQuery,
          ClubsQueryVariables
        >(ClubsDocument, {
          clubsInput: {
            city: cityId,
            categories: [c.id],
          },
          paginationInput: { limit: 8 },
        })();

        if (!clubsWithFilter.docs.length) return null;

        clubsWithFilter.docs.forEach((club) =>
          allSchemas.push(getClubJsonLd(club))
        );

        return (
          <section
            className="w-full flex flex-col items-center container mb-[45px]"
            key={c.id}
          >
            <h2 className="text-2xl lg:text-[40px] font-bold text-text text-center mb-4 lg:mb-6">
              {c.name}
            </h2>
            <div className="w-full flex flex-col items-center justify-center">
              <HorizontalScrollCards>
                {clubsWithFilter?.docs?.map((card) => (
                  <Link
                    href={`/${city?.city?.slug}/clubs/${card.slug}`}
                    key={card.id}
                  >
                    <ClubCard data={card} wrapperClassName="w-[298px]" />
                  </Link>
                ))}
              </HorizontalScrollCards>
              <Link
                className="px-7 py-[11px] font-semibold my-[20px] lg:my-6 bg-primary text-white rounded-full"
                href={`/${city?.city?.slug}/clubs/category/${c.slug}?page=1`}
              >
                View all
              </Link>
            </div>
          </section>
        );
      })
    );
    // Collect schemas for popular clubs
    popularClubs.docs.forEach((club) => allSchemas.push(getClubJsonLd(club)));

    return (
      <>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(
              {
                "@context": "https://schema.org",
                "@type": "ItemList",
                itemListElement: allSchemas.map((schema, index) => ({
                  "@type": "ListItem",
                  position: index + 1,
                  item: schema,
                })),
              },
              null
            ),
          }}
        />

        <div className="bars-section-background-gradient w-full pt-[45px] xl:pt-[98px]">
          {popularClubs?.docs.length > 0 && (
            <section className="w-full flex flex-col container mb-[45px] xl:mb-[88px]">
              <h2 className="text-2xl lg:text-[40px] font-bold text-text  text-center mb-4 lg:mb-6">
                Popular Clubs
              </h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-7 sm:gap-2 xl:gap-[38px]">
                {popularClubs?.docs?.map((club) => (
                  <Link href={`clubs/${club.slug}`} key={club.id}>
                    <GridCard
                      name={club.name}
                      rating={club.rating}
                      avgCost={club.menu?.startingPrice ?? 0}
                      image={club.coverImage!}
                    />
                  </Link>
                ))}
              </div>
            </section>
          )}
          {categorySections}
        </div>
      </>
    );
  } catch (error) {
    return <NotFound error={error as Error} />;
  }
}
