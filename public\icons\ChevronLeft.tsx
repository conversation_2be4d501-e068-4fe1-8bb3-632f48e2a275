import { IconProps } from "@/types";
import * as React from "react";
const ChevronLeftIcon = ({ className, color, height, width }: IconProps) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 11 20"
    fill="none"
    className={className}
  >
    <path
      d="M10 1L1 10L10 19"
      stroke={color}
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export default ChevronLeftIcon;
