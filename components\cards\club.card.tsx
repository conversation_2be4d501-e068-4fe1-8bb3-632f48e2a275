import COLORS from "@/constants/colors";
import { ClubsQuery } from "@/generated/graphql";
import { cn, getInitials } from "@/lib/utils";
import ICONS from "@/public/icons";
import Image from "next/image";
import RatingStars from "../common/RatingStars";

type ClubCardProps = {
  data: ClubsQuery["clubs"]["docs"][number];
  wrapperClassName?: string;
};

function ClubCard({ data, wrapperClassName }: ClubCardProps) {
  if (!data?.slug || !data?.name || !data?.address?.address) return null;
  const name = data?.name;
  const address = data?.address?.address;
  const rating = data?.rating;
  const logo = data?.logo;
  const coverimage = data?.coverImage;
  const tags = data?.tags?.map((c) => c.name);
  const currency = "€";
  const startingPrice = data?.menu?.startingPrice;

  return (
    <div
      className={cn(
        "w-full min-w-[298px] overflow-hidden rounded-[18px] shadow-lg shadow-neutral-300/20 border border-neutral-200/30",
        wrapperClassName
      )}
    >
      <div className="relative w-full h-[198px]">
        {coverimage ? (
          <Image
            src={coverimage}
            fill
            alt="bar"
            className="object-cover"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 298px"
          />
        ) : (
          <Image
            src={"/images/placeholderClubImage.png"}
            fill
            alt="bar"
            className="object-cover"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 298px"
          />
        )}
        <div className="absolute top-4 left-4 bg-white rounded-full">
          <RatingStars className="px-2 py-0.5" ratingValue={rating} />
        </div>
      </div>
      <div className="h-full bg-gradient-to-r from-text from-20% to-secondary to-80%  relative py-5 px-4">
        <div className="flex flex-row items-center absolute top-5 right-4.5 gap-1">
          <ICONS.BeerJugIcon
            width={20}
            height={20}
            color={COLORS.LIGHT.WHITE}
          />
          <p className="font-montserrat text-base font-semibold text-white mt-1">
            {currency} {startingPrice}
          </p>
        </div>
        <div className="absolute w-[69px] h-[68px] rounded-[6px] border-2 border-ring overflow-hidden left-7 -top-8 bg-text flex items-center justify-center">
          {logo ? (
            <Image src={logo} alt="clublogo" fill sizes="69px" />
          ) : (
            <p className="text-2xl font-bold text-white">{getInitials(name)}</p>
          )}
        </div>
        <div className="mt-6 ml-4">
          <div className="font-semibold text-base text-white truncate">
            {name}
          </div>
          <div className="flex flex-row items-center gap-2 mt-2">
            <div className="bg-white min-w-6 min-h-6 rounded-full flex items-center justify-center">
              <ICONS.PinIcon
                width={15}
                height={15}
                color={COLORS.LIGHT.BLACK}
              />
            </div>
            <div className="text-[11px] text-white font-medium truncate max-w-[200px]">
              {address}
            </div>
          </div>
          <div className="flex flex-row items-start gap-2 mt-3">
            <div className="bg-white min-w-6 min-h-6 rounded-full flex items-center justify-center">
              <ICONS.MusicIcon
                width={15}
                height={15}
                color={COLORS.LIGHT.BLACK}
              />
            </div>
            <div className="flex flex-row items-center gap-2 truncate">
              {tags?.slice(0, 2).map((tag, idx) => (
                <div
                  key={idx}
                  className="bg-white rounded-sm flex items-center justify-center px-1 py-1 max-w-[100px]"
                >
                  <p className="text-[11px] font-medium truncate">{tag}</p>
                </div>
              ))}
              {tags.length > 2 && (
                <div className="bg-white rounded-sm flex items-center justify-center px-1 py-1 text-[11px] font-medium">
                  more...
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ClubCard;
