import NotFound from "@/app/not-found";
import { fetchData } from "@/client";
import BarCard from "@/components/cards/bar.card";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import BarsHomePage from "@/elements/bars-homepage";
import {
  BarCategoriesDocument,
  BarCategoriesQuery,
  BarCategoriesQueryVariables,
  BarCategoryByNameDocument,
  BarCategoryByNameQuery,
  BarCategoryByNameQueryVariables,
  BarsDocument,
  BarsQuery,
  BarsQueryVariables,
  CitiesDocument,
  CitiesQuery,
  CitiesQueryVariables,
} from "@/generated/graphql";
import type { Metadata } from "next";
import Link from "next/link";
import { softwareApplicationLd } from "../../page";

export const revalidate = 3600;

export async function generateStaticParams() {
  // fetch all cities
  const citiesRes = await fetchData<CitiesQuery, CitiesQueryVariables>(
    CitiesDocument
  )();
  const cities = citiesRes?.cities?.docs || [];

  // fetch all categories
  const categoriesRes = await fetchData<
    BarCategoriesQuery,
    BarCategoriesQueryVariables
  >(BarCategoriesDocument)();
  const categories = categoriesRes?.barCategories?.docs || [];

  // return all possible city/category combinations
  return cities.flatMap((city) =>
    categories.map((category) => ({
      city: city.slug,
      category: category.slug,
    }))
  );
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ city: string; category: string }>;
}): Promise<Metadata> {
  const { city: cityParam, category: categoryParam } = await params;

  const selectedCityRes = await fetchData<CitiesQuery, CitiesQueryVariables>(
    CitiesDocument,
    { citiesInput: { slug: cityParam } }
  )();

  const cityName = selectedCityRes?.cities?.docs[0].name;

  // fetch category for description
  const categoryRes = await fetchData<
    BarCategoryByNameQuery,
    BarCategoryByNameQueryVariables
  >(BarCategoryByNameDocument, {
    slug: categoryParam,
  })();
  const categoryName = categoryRes?.barCategory?.name;

  const description =
    categoryRes?.barCategory?.description ||
    `Discover the best ${categoryName} bars in ${cityName} with Seeker Social.`;

  return {
    title: `${categoryName} Bars in ${cityName} | Seeker Social`,
    description,
    openGraph: {
      title: `${categoryName} Bars in ${cityName} | Seeker Social`,
      description,
      url: `https://seeker.social/${cityParam}/bars/category/${categoryParam}`,
      siteName: "Seeker Social",
      type: "website",
    },
  };
}

function getBarJsonLd(bar: BarsQuery["bars"]["docs"][number]) {
  return {
    "@context": "https://schema.org",
    "@type": "BarOrPub",
    name: bar.name,
    image: bar.coverImage,
    url: `https://www.seeker.social/${bar.city.slug}/bars/${bar.slug}`,
    description: bar.description,
    address: {
      "@type": "PostalAddress",
      streetAddress: bar.address?.address,
      addressLocality: bar.city.name,
    },
    geo: {
      "@type": "GeoCoordinates",
      latitude: bar.address?.location.center[1],
      longitude: bar.address?.location.center[0],
    },
    telephone: `${bar.contact?.countryCode}${bar.contact?.phone}`,
    openingHours: ["Mo-Su 17:00-02:00"],
    servesCuisine: bar.menu?.sections.map((section) => section.name),
  };
}

export default async function CategoryPage({
  params,
  searchParams,
}: {
  params: Promise<{ city: string; category: string }>;
  searchParams: Promise<{ page: string }>;
}) {
  const { category: categoryParam, city: cityParam } = await params;
  const { page } = await searchParams;
  const selectedCityRes = await fetchData<CitiesQuery, CitiesQueryVariables>(
    CitiesDocument,
    { citiesInput: { slug: cityParam } }
  )();
  const city = selectedCityRes?.cities?.docs[0];

  const currentPage = Number(page) || 1;

  // Fetch bar categories with error handling
  const barCategoriesResponse = await fetchData<
    BarCategoriesQuery,
    BarCategoriesQueryVariables
  >(BarCategoriesDocument)();

  if (!barCategoriesResponse?.barCategories) {
    return (
      <NotFound
        error={
          new Error("Something went wrong while fetching the bar categories...")
        }
      />
    );
  }

  const { barCategories } = barCategoriesResponse;

  const categoryRes = await fetchData<
    BarCategoryByNameQuery,
    BarCategoryByNameQueryVariables
  >(BarCategoryByNameDocument, {
    slug: categoryParam,
  })();
  const categoryName = categoryRes?.barCategory?.name;

  if (!categoryRes?.barCategory) {
    return (
      <NotFound
        error={new Error("We couldn't find the page you are looking for...")}
      />
    );
  }
  const barsResponse = await fetchData<BarsQuery, BarsQueryVariables>(
    BarsDocument,
    {
      barsInput: {
        city: city?._id,
        categories: categoryRes?.barCategory?.id
          ? [categoryRes.barCategory.id]
          : undefined,
      },
      paginationInput: { limit: 8, page: currentPage },
    }
  )();

  // Check if bars response is valid
  if (!barsResponse?.bars) {
    return <NotFound error={new Error("No bars found")} />;
  }

  const { bars: barsWithFilter } = barsResponse;

  if (barsWithFilter.docs === undefined) {
    return <NotFound error={new Error("No bars found")} />;
  }

  return (
    <div>
      <BarsHomePage.HeroSection
        category={categoryName}
        description={categoryRes?.barCategory?.description}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(
            {
              "@context": "https://schema.org",
              "@type": "ItemList",
              itemListElement: barsWithFilter.docs.map((schema, index) => ({
                "@type": "ListItem",
                position: index + 1,
                item: getBarJsonLd(schema),
              })),
            },
            null,
            2
          ),
        }}
      />
      <script
        key="software-jsonld"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(softwareApplicationLd).replace(
            /</g,
            "\\u003c"
          ),
        }}
      />
      <section id="categoryList" className="container">
        <div className="flex flex-row flex-wrap items-center justify-center gap-5 my-[65px] lg:my-[76px] ">
          {barsWithFilter?.docs?.map((bar) => (
            <Link
              href={`/${city.slug}/bars/${bar.slug}`}
              key={bar.id}
              className="w-[298px]"
            >
              <BarCard data={bar} wrapperClassName="w-[298px]" />
            </Link>
          ))}
          <div className="w-full mt-2 lg:mt-6">
            <Pagination>
              <PaginationContent>
                {/* Previous Page */}
                {barsWithFilter.hasPrevPage && (
                  <PaginationItem>
                    <PaginationPrevious
                      href={`?page=${barsWithFilter.prevPage}`}
                    />
                  </PaginationItem>
                )}

                {/* First Page */}
                <PaginationItem>
                  <PaginationLink href={`?page=1`} isActive={currentPage === 1}>
                    1
                  </PaginationLink>
                </PaginationItem>

                {/* Second Page */}
                {barsWithFilter.totalPages > 1 && (
                  <PaginationItem>
                    <PaginationLink
                      href={`?page=2`}
                      isActive={currentPage === 2}
                    >
                      2
                    </PaginationLink>
                  </PaginationItem>
                )}

                {/* Ellipsis if currentPage is far from beginning */}
                {currentPage > 4 && (
                  <PaginationItem>
                    <PaginationEllipsis />
                  </PaginationItem>
                )}

                {/* Current Page (if not 1 or 2 or last 2) */}
                {currentPage > 2 &&
                  currentPage < barsWithFilter.totalPages - 1 && (
                    <PaginationItem>
                      <PaginationLink href={`?page=${currentPage}`} isActive>
                        {currentPage}
                      </PaginationLink>
                    </PaginationItem>
                  )}

                {/* Ellipsis before last 2 pages */}
                {currentPage < barsWithFilter.totalPages - 3 && (
                  <PaginationItem>
                    <PaginationEllipsis />
                  </PaginationItem>
                )}

                {/* Second Last Page */}
                {barsWithFilter.totalPages > 3 && (
                  <PaginationItem>
                    <PaginationLink
                      href={`?page=${barsWithFilter.totalPages - 1}`}
                      isActive={currentPage === barsWithFilter.totalPages - 1}
                    >
                      {barsWithFilter.totalPages - 1}
                    </PaginationLink>
                  </PaginationItem>
                )}

                {/* Last Page */}
                {barsWithFilter.totalPages > 2 && (
                  <PaginationItem>
                    <PaginationLink
                      href={`?page=${barsWithFilter.totalPages}`}
                      isActive={currentPage === barsWithFilter.totalPages}
                    >
                      {barsWithFilter.totalPages}
                    </PaginationLink>
                  </PaginationItem>
                )}

                {/* Next Page */}
                {barsWithFilter.hasNextPage && (
                  <PaginationItem>
                    <PaginationNext href={`?page=${barsWithFilter.nextPage}`} />
                  </PaginationItem>
                )}
              </PaginationContent>
            </Pagination>
          </div>
        </div>
        <div className="flex flex-col gap-4 lg:gap-[28px] container">
          <div className="font-bold text-2xl leading-5 text-secondary">
            Explore Other Bars
          </div>
          <div className="flex flex-row items-center flex-wrap gap-2 sm:gap- mb-[72px]">
            {barCategories?.docs
              ?.filter((category) => category.name !== categoryName)
              .map((category) => (
                <Link
                  key={category.id}
                  href={`/${city.slug}/bars/category/${category.slug}?page=1`}
                  className="px-3 py-1.5 text-sm rounded-none sm:px-[28px] lg:text-xl font-bold leading-5 bg-primary text-white hover:text-background/80 transition-all duration-100 ease-in-out whitespace-nowrap"
                >
                  {category.name}
                </Link>
              ))}
          </div>
        </div>
      </section>
    </div>
  );
}
