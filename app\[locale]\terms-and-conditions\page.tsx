import Link from "next/link";
import React from "react";

export default function TermsAndConditions() {
  return (
    <>
      <div className="w-full py-8 lg:py-[75px] contact-banner-gradient flex items-center justify-center">
        <h1 className="text-3xl lg:text-[40px] text-white font-bold container text-center">
          Terms and Conditions
        </h1>
      </div>
      <div className="container my-4 lg:py-[80px] lg:px-8">
        <div className="text-secondary font-medium text-base lg:text-xl font-montserrat mb-4 lg:mb-6">
          Welcome to Seeker Social! These Terms and Conditions
          (&quot;Terms&quot;) govern your use of the Seeker Social mobile
          application (the &quot;App&quot;), provided by Seeker Social
          (&quot;we,&quot; &quot;us,&quot; or &quot;our&quot;). By accessing or
          using our App, you agree to be bound by these Terms.
        </div>
        <h3 className="text-text text-base lg:text-xl font-montserrat font-bold mb-1">
          1. Acceptance of Terms
        </h3>
        <p className="text-text text-sm lg:text-lg font-montserrat mb-4 lg:mb-6">
          By using the App, you confirm that you have read, understood, and
          agree to these Terms. If you do not agree with these Terms, you must
          not use the App.
        </p>
        <h3 className="text-text text-base lg:text-xl font-montserrat font-bold mb-1">
          2. Eligibility
        </h3>
        <p className="text-text text-sm lg:text-lg font-montserrat mb-4 lg:mb-6">
          You must be at least 18 years of age to use this App. By using the
          App, you represent and warrant that you meet this age requirement.
        </p>
        <h3 className="text-text text-base lg:text-xl font-montserrat font-bold mb-1">
          3. Use of the App
        </h3>
        <p className="text-text text-sm lg:text-lg font-montserrat mb-1">
          Seeker Social grants you a limited, non-exclusive, non-transferable
          license to use the App for your personal, non-commercial use.
        </p>
        <div className="flex flex-row items-start mb-1 ml-2 lg:ml-4">
          <div className="min-w-2 min-h-2 rounded-full bg-text mt-3 mr-2" />
          <p className="text-text text-sm lg:text-lg font-montserrat">
            <span className="text-text text-base lg:text-lg font-montserrat font-bold">
              Listings:{" "}
            </span>{" "}
            The App provides listings of bars, clubs, and other venues,
            including information about their menus and offerings. This
            information is for informational purposes only.
          </p>
        </div>
        <div className="flex flex-row items-start mb-4 lg:mb-6 ml-2 lg:ml-4">
          <div className="min-w-2 min-h-2 rounded-full bg-text mt-3 mr-2" />
          <p className="text-text text-sm lg:text-lg font-montserrat">
            <span className="text-text text-base lg:text-lg font-montserrat font-bold">
              Accuracy of Information:
            </span>{" "}
            We strive to provide accurate information, but we do not guarantee
            the accuracy, completeness, or timeliness of any information on the
            App, including menus, hours, or prices. This information is subject
            to change without notice. The menu information is presented in a
            text format and is extracted by us; it is not an official
            representation from the venue.
          </p>
        </div>
        <h3 className="text-text text-base lg:text-xl font-montserrat font-bold mb-1">
          4. User Accounts (Optional)
        </h3>
        <p className="text-text text-sm lg:text-lg font-montserrat mb-1">
          You can browse the App without creating an account. However, to access
          certain features, such as saving favorite venues, you must create an
          account.
        </p>
        <div className="flex flex-row items-start mb-1 ml-2 lg:ml-4">
          <div className="min-w-2 min-h-2 rounded-full bg-text mt-3 mr-2" />
          <p className="text-text text-sm lg:text-lg font-montserrat">
            <span className="text-text text-base lg:text-lg font-montserrat font-bold">
              Account Creation:
            </span>{" "}
            When you create an account, you agree to provide accurate and
            complete information, such as your email address.
          </p>
        </div>
        <div className="flex flex-row items-start mb-4 lg:mb-6 ml-2 lg:ml-4">
          <div className="min-w-2 min-h-2 rounded-full bg-text mt-3 mr-2" />
          <p className="text-text text-sm lg:text-lg font-montserrat">
            <span className="text-text text-base lg:text-lg font-montserrat font-bold">
              Account Responsibility:
            </span>{" "}
            You are responsible for maintaining the confidentiality of your
            account information and for all activities that occur under your
            account.
          </p>
        </div>
        <h3 className="text-text text-base lg:text-xl font-montserrat font-bold mb-1">
          5. User Content
        </h3>
        <p className="text-text text-sm lg:text-lg font-montserrat mb-4 lg:mb-6">
          Currently, users are not permitted to post content, such as reviews or
          photos. We reserve the right to introduce such features in the future.
          If we do, these Terms will be updated, and you will be notified of the
          changes.
        </p>
        <h3 className="text-text text-base lg:text-xl font-montserrat font-bold mb-1">
          6. Prohibited Conduct
        </h3>
        <p className="text-text text-sm lg:text-lg font-montserrat mb-1">
          You agree not to:
        </p>
        <div className="flex flex-row items-start mb-1 ml-2 lg:ml-4">
          <div className="min-w-2 min-h-2 rounded-full bg-text mt-3 mr-2" />
          <p className="text-text text-sm lg:text-lg font-montserrat">
            Use the App for any illegal purpose or in violation of any local,
            national, or international law.
          </p>
        </div>
        <div className="flex flex-row items-start mb-1 ml-2 lg:ml-4">
          <div className="min-w-2 min-h-2 rounded-full bg-text mt-3 mr-2" />
          <p className="text-text text-sm lg:text-lg font-montserrat">
            Attempt to reverse engineer, decompile, or otherwise access the
            source code of the App.
          </p>
        </div>
        <div className="flex flex-row items-start mb-4 lg:mb-6 ml-2 lg:ml-4">
          <div className="min-w-2 min-h-2 rounded-full bg-text mt-3 mr-2" />
          <p className="text-text text-sm lg:text-lg font-montserrat">
            Use any automated system to scrape or extract data from the App for
            commercial purposes.
          </p>
        </div>
        <h3 className="text-text text-base lg:text-xl font-montserrat font-bold mb-1">
          7. Intellectual Property
        </h3>
        <p className="text-text text-sm lg:text-lg font-montserrat mb-4 lg:mb-6">
          All content and materials within the App, including the design, text,
          graphics, and logos, are the property of Seeker Social or are used
          with permission. You may not use, copy, or distribute any content from
          the App without our prior written consent.
        </p>
        <h3 className="text-text text-base lg:text-xl font-montserrat font-bold mb-1">
          8. Limitation of Liability
        </h3>
        <p className="text-text text-sm lg:text-lg font-montserrat mb-4 lg:mb-6">
          To the fullest extent permitted by law, Seeker Social shall not be
          liable for any indirect, incidental, special, or consequential damages
          resulting from your use of or inability to use the App. We are not
          responsible for your experience at any of the venues listed in the
          App.
        </p>
        <h3 className="text-text text-base lg:text-xl font-montserrat font-bold mb-1">
          9. Governing Law
        </h3>
        <p className="text-text text-sm lg:text-lg font-montserrat mb-4 lg:mb-6">
          These Terms shall be governed by and construed in accordance with the
          laws of France.
        </p>
        <h3 className="text-text text-base lg:text-xl font-montserrat font-bold mb-1">
          10. Changes to Terms
        </h3>
        <p className="text-text text-sm lg:text-lg font-montserrat mb-4 lg:mb-6">
          We reserve the right to modify these Terms at any time. We will notify
          you of any changes by posting the new Terms within the App. Your
          continued use of the App after such changes constitutes your
          acceptance of the new Terms.
        </p>
        <h3 className="text-text text-base lg:text-xl font-montserrat font-bold mb-1">
          11. Contact Us
        </h3>
        <p className="text-text text-sm lg:text-lg font-montserrat mb-4 lg:mb-6">
          If you have any questions or any copyright issues, please contact us
          at{" "}
          <Link
            className="font-semibold text-blue-400 hover:underline hover:underline-offset-2"
            href={"mailto:<EMAIL>"}
          >
            <EMAIL>.
          </Link>
        </p>
      </div>
    </>
  );
}
