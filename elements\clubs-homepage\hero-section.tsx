import React from "react";

export const revalidate = 3600;

export default function HeroSection({
  category,
  description,
}: {
  category: string | undefined;
  description: string | undefined | null;
}) {
  return (
    <section className="w-full py-[112px] md:py-[128px] clubs-banner-gradient flex flex-col items-center justify-center">
      <div className="container w-full flex flex-col items-center justify-center px-4">
        {category ? (
          <div className="font-bold text-white text-[32px] leading-10.5 text-center sm:text-[40px] sm:leading-[52px]">
            {category}
          </div>
        ) : (
          <div className="font-bold text-white text-[32px] leading-10.5 text-center sm:text-[40px] sm:leading-[52px]">
            Clubs
          </div>
        )}
        {category ? (
          <p className="text-sm text-footerbg sm:text-xl mt-[40px] text-center">
            {description}
          </p>
        ) : (
          <p className="text-sm text-footerbg sm:text-xl mt-[40px] text-center">
            Discover France&apos;s club nightlife with an insider&apos;s guide
            to the best clubs for all kinds of moods and music taste. From VIP
            clubs with bottle service, exclusive table reservations to
            underground techno clubs where the parties run till sunrise, we have
            options for all. Dance all night to the hip hop beats, shake it to
            Latino hits, or vibe to house, electro, afrobeat, or disco in themed
            club nights. Want to celebrate in style? You can easily find
            detailed table prices, minimum spends, bottle menus, and dress code
            information so there are no surprises at the door
          </p>
        )}
      </div>
    </section>
  );
}
