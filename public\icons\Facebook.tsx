import { IconProps } from "@/types";
import * as React from "react";
const FacebookIcon = ({ className, color, height, width }: IconProps) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 15 16"
    fill="none"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M6.5932 7.99993H7.91588V12.2438H9.33052V7.99993H10.731V6.58529H9.33052V5.87798C9.33052 5.48895 9.64881 5.17066 10.0378 5.17066H10.7451V3.75603H10.0378C8.86369 3.75603 7.91588 4.70383 7.91588 5.87798V6.58529H6.5932V7.99993ZM1.55003 3.2609V12.746C1.55003 13.2553 1.96027 13.6655 2.46954 13.6655H11.9547C12.4639 13.6655 12.8742 13.2553 12.8742 12.746V3.2609C12.8742 2.75164 12.4639 2.34139 11.9547 2.34139H2.46247C1.9532 2.34139 1.54296 2.75164 1.54296 3.2609H1.55003ZM2.33515 0.926758H11.8698C13.143 0.926758 14.1756 1.95944 14.1756 3.23261V12.7672C14.1756 14.0404 13.143 15.0731 11.8698 15.0731H2.33515C1.06198 15.0731 0.0292969 14.0404 0.0292969 12.7672V3.23261C0.0292969 1.95944 1.06198 0.926758 2.33515 0.926758Z"
      fill={color}
    />
  </svg>
);
export default FacebookIcon;
