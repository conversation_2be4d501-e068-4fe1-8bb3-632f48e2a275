import { isAxiosError } from "axios";
import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { toast } from "sonner";
import { setHours, setMinutes, setSeconds } from "date-fns";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Converts a number like 1700 into a Date object set to 5:00 PM.
 *
 * @param time - Time in 24-hour format as a number (e.g. 930, 1700, 1830)
 * @param baseDate - Optional base Date (defaults to today)
 * @returns Date object with the specified time
 */
export function parseTimeToDate(
  time: number,
  baseDate: Date = new Date()
): Date {
  const hours = Math.floor(time / 100);
  const minutes = time % 100;
  return setSeconds(setMinutes(setHours(baseDate, hours), minutes), 0);
}

/**
 * Converts military time (e.g., 1500, 830, 30) to a formatted 12-hour time string (e.g., "3 PM", "8:30 AM")
 * @param militaryTime - A number like 1500, 845, 30 etc.
 * @returns Formatted time string like "3 PM", "8:45 AM"
 */
export function formatMilitaryTime(time: number): string {
  const hours = Math.floor(time / 100);
  const minutes = time % 100;

  const isAM = hours < 12 || hours === 24;

  const adjustedHours =
    hours === 0 || hours === 24 ? 12 : hours > 12 ? hours - 12 : hours;

  const formattedMinutes = minutes.toString().padStart(2, "0");

  return `${adjustedHours}:${formattedMinutes} ${isAM ? "AM" : "PM"}`;
}

export function errorHandler(ex: unknown) {
  let errorMessage = "Something went wrong";
  if (isAxiosError(ex)) {
    errorMessage = ex.response?.data?.message || ex.message || "Error…";
  } else if (ex instanceof Error) {
    errorMessage = ex.message;
  }

  toast(errorMessage, {
    description: "Please try again.",
    icon: "❌",
    duration: 4000,
    style: { backgroundColor: "#f87171", color: "#fff" }, // optional error styling
  });
}

export function getInitials(name: string): string {
  if (!name) return "";

  const words = name.trim().split(/\s+/); // splits by any whitespace
  const initials = words.slice(0, 2).map((word) => word[0].toUpperCase());

  return initials.join("");
}

export const DAYS = [
  "MONDAY",
  "TUESDAY",
  "WEDNESDAY",
  "THURSDAY",
  "FRIDAY",
  "SATURDAY",
  "SUNDAY",
];

export function slugify(str: string) {
  return str
    .toLowerCase()
    .trim()
    .replace(/\s+/g, "-") // spaces → dashes
    .replace(/&/g, "and") // & → and
    .replace(/[^\w-]+/g, ""); // remove non-url chars
}

export function unslugify(str: string) {
  return str
    .split("-") // split on dashes
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1)) // capitalize
    .join(" "); // join with space
}
