import NotFound from "@/app/not-found";
import { fetchData } from "@/client";
import ClubDetail from "@/elements/club-detail-page";
import {
  ClubsDocument,
  ClubsQuery,
  ClubsQueryVariables,
} from "@/generated/graphql";
import { unslugify } from "@/lib/utils";
import { Metadata } from "next";

export const revalidate = 3600;
type GetJsonLdProps = {
  faqDetails: ClubsQuery["clubs"]["docs"][0]["faqs"];
};

export async function generateStaticParams() {
  const res = await fetchData<ClubsQuery, ClubsQueryVariables>(ClubsDocument)();

  const clubs = res?.clubs?.docs || [];

  return clubs.map((club) => ({
    club: club.slug,
  }));
}

export const generateMetadata = async ({
  params,
}: {
  params: Promise<{
    club: string;
  }>;
}): Promise<Metadata> => {
  const { club: slugParam } = await params;
  try {
    const res = await fetchData<ClubsQuery, ClubsQueryVariables>(
      ClubsDocument,
      {
        clubsInput: { slug: slugParam },
        paginationInput: { limit: 1 },
      }
    )();

    const club = res?.clubs?.docs?.[0];
    if (!club) {
      return {
        title: "Club Not Found | Seeker Social",
        description:
          "The requested club could not be found. Discover other amazing nightlife spots on Seeker Social.",
      };
    }

    // Fallback values for better SEO
    const title = club.seo?.metaTitle || club.name || "Club Details";
    const description =
      club.seo?.metaDescription ||
      club.description ||
      `Discover ${club.name} on Seeker Social`;
    const ogTitle = club.seo?.ogTitle || title;
    const ogDescription = club.seo?.ogDescription || description;
    const canonicalUrl = club.seo?.canonicalUrl;

    const metadata: Metadata = {
      title,
      description,
      openGraph: {
        title: ogTitle,
        description: ogDescription,
        type: "website",
        siteName: "Seeker Social",
        locale: "en_US",
        ...(canonicalUrl && { url: canonicalUrl }),
        ...(club.coverImage && {
          images: [
            {
              url: club.coverImage,
              width: 1200,
              height: 630,
              alt: `${club.name} cover image`,
            },
          ],
        }),
      },
      twitter: {
        card: "summary_large_image",
        title: ogTitle,
        description: ogDescription,
        ...(club.coverImage && {
          images: [club.coverImage],
        }),
      },
      // Add canonical URL if available
      ...(canonicalUrl && {
        alternates: {
          canonical: canonicalUrl,
        },
      }),
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          "max-video-preview": -1,
          "max-image-preview": "large",
          "max-snippet": -1,
        },
      },
    };

    return metadata;
  } catch (error) {
    console.log(error);
    return {
      title: `Club Not Found | Seeker Social`,
      description:
        "The requested bar could not be found. Discover other amazing bars and nightlife spots on Seeker Social.",
      robots: {
        index: false,
        follow: true,
      },
    };
  }
};

function generateMenuJsonLD(club: ClubsQuery["clubs"]["docs"][number]) {
  if (!club.menu?.sections || club.menu.sections.length === 0) {
    return null;
  }
  const jsonLD = {
    "@context": "https://schema.org",
    "@type": "Menu",
    name: club.name,
    url: `https://seeker.social/${club.city.slug}/clubs/${club.slug}`,
    hasMenuSection: club.menu?.sections.map((section) => ({
      "@type": "MenuSection",
      name: section.name,
      menuItem: section.items.map((item) => ({
        "@type": "MenuItem",
        name: item.name,
        offers: [
          {
            "@type": "Offer",
            price: item.price,
            priceCurrency: "EUR",
          },
        ],
      })),
    })),
  };

  return JSON.stringify(jsonLD, null, 2);
}

function generateJsonLd(club: ClubsQuery["clubs"]["docs"][number]) {
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "NightClub",
    name: club.name,
    image: club.coverImage,
    url: `https://www.seeker.social/${club.city.slug}/clubs/${club.slug}`,
    description: club.description,
    address: {
      "@type": "PostalAddress",
      streetAddress: club.address?.address,
      addressLocality: club.city.name,
    },
    geo: {
      "@type": "GeoCoordinates",
      latitude: club.address?.location.center[1],
      longitude: club.address?.location.center[0],
    },
    telephone: `${club.contact?.countryCode}${club.contact?.phone}`,
    openingHours: ["Mo-Su 17:00-02:00"],
    servesCuisine: club.menu?.sections.map((section) => section.name),
  };

  return JSON.stringify(jsonLd, null, 2);
}

function getFAQJsonLd({ faqDetails }: GetJsonLdProps) {
  if (!faqDetails || faqDetails.length === 0) {
    return null;
  }

  const faqSchema = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    mainEntity: faqDetails.map((faq) => ({
      "@type": "Question",
      name: faq.title,
      acceptedAnswer: {
        "@type": "Answer",
        text: faq.content,
      },
    })),
  };

  return JSON.stringify(faqSchema);
}

export default async function ClubDetailPage({
  params,
}: {
  params: Promise<{ club: string; city: string }>;
}) {
  const { club: slugParam, city: cityParam } = await params;

  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: [
      {
        "@type": "ListItem",
        position: 1,
        name: "Home",
        item: "https://seeker.social",
      },
      {
        "@type": "ListItem",
        position: 2,
        name: cityParam,
        item: `https://seeker.social/${cityParam}`,
      },
      {
        "@type": "ListItem",
        position: 3,
        name: "Clubs",
        item: `https://seeker.social/${cityParam}/clubs`,
      },
      {
        "@type": "ListItem",
        position: 4,
        name: unslugify(slugParam),
        item: `https://seeker.social/${cityParam}/clubs/${slugParam}`,
      },
    ],
  };

  try {
    const res = await fetchData<ClubsQuery, ClubsQueryVariables>(
      ClubsDocument,
      {
        clubsInput: { slug: slugParam },
        paginationInput: { limit: 1 },
      }
    )();
    const club = res.clubs.docs[0];

    const { clubs: otherClubs } = await fetchData<
      ClubsQuery,
      ClubsQueryVariables
    >(ClubsDocument, {
      clubsInput: { city: club.city.id, featured: true },
    })();
    const menuJsonLD = generateMenuJsonLD(club);
    const listingJsonLd = generateJsonLd(club);
    const faqJsonLd = getFAQJsonLd({ faqDetails: club.faqs });

    return (
      <div className="bg-backgroundlight sm:bg-transparent">
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(jsonLd).replace(/</g, "\\u003c"),
          }}
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: listingJsonLd.replace(/</g, "\\u003c"),
          }}
        />
        {menuJsonLD && (
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{
              __html: menuJsonLD.replace(/</g, "\\u003c"),
            }}
          />
        )}
        {faqJsonLd && (
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{
              __html: faqJsonLd.replace(/</g, "\\u003c"),
            }}
          />
        )}
        <ClubDetail.ClubDetailSection club={club} otherClubs={otherClubs} />
      </div>
    );
  } catch (error) {
    return <NotFound error={error as Error} />;
  }
}
