import NotFound from "@/app/not-found";
import { fetchData } from "@/client";
import BarsHomePage from "@/elements/bars-homepage";
import {
  BarCategoriesDocument,
  BarCategoriesQuery,
  BarCategoriesQueryVariables,
  BarCategoryByNameDocument,
  BarCategoryByNameQuery,
  BarCategoryByNameQueryVariables,
  BarsDocument,
  BarsQuery,
  BarsQueryVariables,
  CitiesDocument,
  CitiesQuery,
  CitiesQueryVariables,
} from "@/generated/graphql";
import { Metadata } from "next";

export const revalidate = 3600;

export async function generateStaticParams() {
  const res = await fetchData<CitiesQuery, CitiesQueryVariables>(
    CitiesDocument,
    {}
  )();

  const cities = res?.cities?.docs || [];

  return cities.map((city) => ({
    city: city.slug,
    coverImage: city.coverImage,
  }));
}

export const generateMetadata = async ({
  params,
}: {
  params: Promise<{ city: string; coverImage: string }>;
}): Promise<Metadata> => {
  const { city: citySlug, coverImage: coverImageParam } = await params;

  const selectedCityRes = await fetchData<CitiesQuery, CitiesQueryVariables>(
    CitiesDocument,
    { citiesInput: { slug: citySlug } }
  )();

  const city = selectedCityRes?.cities?.docs[0];

  return {
    title: `Find the Best Bars with Happy Hours in ${city.name} - Seeker Social`,
    description: `Discover all kinds of bars with great happy hour deals near you, perfect for students, afterwork drinks, or nights out with friends. Cheap cocktails, beers, and the best terraces in town`,
    openGraph: {
      title: `Find the Best Bars with Happy Hours in ${city.name} - Seeker Social`,
      description: `Discover all kinds of bars with great happy hour deals near you, perfect for students, afterwork drinks, or nights out with friends. Cheap cocktails, beers, and the best terraces in town`,
      url: `https://seeker.com/${citySlug}/bars`,
      images: [
        {
          url: coverImageParam,
          width: 1200,
          height: 630,
          alt: `Bars in ${city.name}`,
        },
      ],
    },
  };
};

//SOFTWARE-APPLICATION-SCHEMA
export const softwareApplicationLd = {
  "@context": "https://schema.org",
  "@type": "SoftwareApplication",
  name: "Seeker Social - Find Bars and Clubs in France",
  operatingSystem: "ANDROID, iOS, Web",
  applicationCategory: "MobileApplication",
  url: "https://seeker.social",
  sameAs: [
    "https://instagram.com/seeker.social",
    "https://tiktok.com/@seeker.social",
  ],
  softwareVersion: "1.0.2",
  author: {
    "@type": "Organization",
    name: "Seeker Social",
    url: "https://seeker.social",
  },
  description:
    "Seeker Social helps users discover bars and clubs offering happy hours in France. Users can filter by city, category, and book directly from the app.",
  offers: [
    {
      "@type": "Offer",
      url: "https://play.google.com/store/apps/details?id=com.seekersocial.app",
      price: "0",
      priceCurrency: "EUR",
      platform: "Android",
    },
    {
      "@type": "Offer",
      url: "https://apps.apple.com/fr/app/seeker-social-find-nightlife/id6749886146",
      price: "0",
      priceCurrency: "EUR",
      platform: "iOS",
    },
    {
      "@type": "Offer",
      url: "https://seeker.social",
      price: "0",
      priceCurrency: "EUR",
      platform: "Web",
    },
  ],
  aggregateRating: {
    "@type": "AggregateRating",
    ratingValue: "4.8",
    reviewCount: "120",
  },
};

export default async function Bars({
  params,
  searchParams,
}: {
  params: Promise<{ city: string }>;
  searchParams: Promise<{ [key: string]: string | undefined }>;
}) {
  const { category, page } = await searchParams;
  const currentPage = Number(page) || 1;

  const { city: cityParam } = await params;

  try {
    // Fetch cities with error handling
    const citiesResponse = await fetchData<CitiesQuery, CitiesQueryVariables>(
      CitiesDocument,
      {
        citiesInput: {
          slug: cityParam,
        },
      }
    )();

    if (!citiesResponse?.cities?.docs?.[0]) {
      return (
        <NotFound
          error={new Error("We couldn't find the page you are looking for...")}
        />
      );
    }

    const city = citiesResponse.cities.docs[0];

    // Fetch bar categories with error handling
    const barCategoriesResponse = await fetchData<
      BarCategoriesQuery,
      BarCategoriesQueryVariables
    >(BarCategoriesDocument)();

    if (!barCategoriesResponse?.barCategories) {
      return (
        <NotFound
          error={
            new Error(
              "Something went wrong while fetching the bar categories..."
            )
          }
        />
      );
    }

    const { barCategories } = barCategoriesResponse;

    let categoryRes: BarCategoryByNameQuery | undefined;

    if (category?.length) {
      const res = await fetchData<
        BarCategoryByNameQuery,
        BarCategoryByNameQueryVariables
      >(BarCategoryByNameDocument, { slug: category })();
      categoryRes = res;
    }

    // Fetch bars with proper error handling
    const barsResponse = await fetchData<BarsQuery, BarsQueryVariables>(
      BarsDocument,
      {
        barsInput: {
          city: city._id,
          categories: categoryRes?.barCategory?.id
            ? [categoryRes.barCategory.id]
            : undefined,
        },
        paginationInput: { limit: 8, page: currentPage },
      }
    )();

    // Check if bars response is valid
    if (!barsResponse?.bars) {
      return <NotFound error={new Error("No bars found")} />;
    }

    const { bars: barsWithFilter } = barsResponse;

    if (barsWithFilter.docs === undefined) {
      return <NotFound error={new Error("No bars found")} />;
    }

    return (
      <>
        <script
          key="software-jsonld"
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(softwareApplicationLd).replace(
              /</g,
              "\\u003c"
            ),
          }}
        />
        <BarsHomePage.HeroSection
          category={category}
          description={categoryRes?.barCategory?.description}
        />

        <BarsHomePage.BestBarsSection
          bars={barsWithFilter}
          city={city}
          categories={barCategories?.docs}
        />
        <BarsHomePage.BarsSection cityId={city.id} />
        <BarsHomePage.CallToDownloadSection />
      </>
    );
  } catch (error) {
    return <NotFound error={error as Error} />;
  }
}
