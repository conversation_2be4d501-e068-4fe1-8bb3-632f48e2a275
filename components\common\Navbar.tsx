"use client";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>r<PERSON>ontent,
  <PERSON>ubarMenu,
  <PERSON>ubarTrigger,
} from "@/components/ui/menubar";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>rigger,
} from "@/components/ui/sheet";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import COLORS from "@/constants/colors";
import { CitiesQuery } from "@/generated/graphql";
import { cn } from "@/lib/utils";
import ICONS from "@/public/icons";
import Image from "next/image";
import Link from "next/link";
import React, { useState } from "react";
import DownloadAppButton from "./DownloadButton";

type City = CitiesQuery["cities"];

function ListItem({
  divider,
  text,
  cityName,
  href,
  ...props
}: React.ComponentPropsWithoutRef<"li"> & {
  href: string;
  divider?: boolean;
  text: string;
  cityName: string;
}) {
  const [isHover, setIsHover] = useState(false);

  const handleHover = () => {
    setIsHover(true);
  };

  const handleLeave = () => {
    setIsHover(false);
  };

  return (
    <>
      <li
        className="bg-transparent transition-colors duration-300 ease-in-out rounded-[3px] px-2 py-4 hover:bg-primary/10 w-full"
        onMouseEnter={handleHover}
        onMouseLeave={handleLeave}
        {...props}
      >
        <Link
          href={href}
          className="flex flex-row items-center gap-2 text-sm font-medium"
        >
          <span className={cn("text-sm font-bold", isHover && "text-primary")}>
            {text} {cityName}
          </span>
        </Link>
      </li>
      {divider && <hr className="border-footerbg/30" />}
    </>
  );
}

export default function Navbar(cities: City) {
  const { docs } = cities;

  const [open, setOpen] = useState(false);

  return (
    <>
      {/* WEB VIEW */}
      <nav className="w-full container py-4 bg-background sm:flex sm:items-center sm:justify-between hidden">
        <Link href={"/"}>
          <Image
            src="/logo.svg"
            width={130}
            height={40}
            alt="logo"
            className="w-[130px] h-[40px] lg:w-[175px] lg:h-[57px]"
            priority={true}
            fetchPriority="high"
            loading="eager"
          />
        </Link>
        <div className="flex flex-1 justify-end mt-2">
          <Menubar>
            <MenubarMenu>
              <MenubarTrigger className="md:text-sm xl:w-[105px] lg:text-base">
                Bars
              </MenubarTrigger>
              <MenubarContent className="p-4 transition-all duration-400 ease-in-out">
                <ul className="gap-2 md:w-[150px] lg:w-[250px] ">
                  {/* City Links */}
                  {docs?.map((city, idx) => (
                    <ListItem
                      key={city.id}
                      href={`/${city.slug}/bars`}
                      divider={docs.length - 1 !== idx}
                      text="Bars in"
                      cityName={city.name}
                    />
                  ))}
                </ul>
              </MenubarContent>
            </MenubarMenu>
            <MenubarMenu>
              <MenubarTrigger className="md:text-sm xl:w-[105px] lg:text-base">
                Clubs
              </MenubarTrigger>
              <MenubarContent className="p-4 transition-all duration-300 ease-in-out">
                <ul className="gap-2 md:w-[200px] lg:w-[250px] ">
                  {/* City Links */}
                  {docs?.map((city, idx) => (
                    <ListItem
                      key={city.id}
                      href={`/${city.slug}/clubs`}
                      divider={docs.length - 1 !== idx}
                      text="Clubs in"
                      cityName={city.name}
                    />
                  ))}
                </ul>
              </MenubarContent>
            </MenubarMenu>
            <MenubarMenu>
              <Tooltip>
                <TooltipTrigger asChild>
                  <MenubarTrigger
                    className="md:text-sm xl:w-[105px] lg:text-base"
                    icon={false}
                  >
                    Events
                  </MenubarTrigger>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-sm font-medium text-primary">
                    Coming Soon
                  </p>
                </TooltipContent>
              </Tooltip>
            </MenubarMenu>
            <MenubarMenu>
              <MenubarTrigger
                className="md:text-sm xl:w-[120px] lg:text-base"
                icon={false}
              >
                <Link href={"/contact"}>Contact Us</Link>
              </MenubarTrigger>
            </MenubarMenu>
            <Link
              href={"/#download-app"}
              className="px-3 py-3 ml-4 text-xs lg:text-base lg:px-7 lg:py-3.5 rounded-full bg-gradient-to-br from-muted-primary via-primary to-primary font-semibold text-white inline-block border-0"
            >
              DOWNLOAD APP
            </Link>
          </Menubar>
        </div>
      </nav>
      {/* MOBILE VIEW */}
      <nav className="w-full py-5 px-4 relative bg-background flex flex-row items-center justify-between sm:hidden">
        <Link href={"/"}>
          <Image
            src="/logo.svg"
            width={110}
            height={36}
            alt="logo"
            className="w-[110px] h-[36px]"
          />
        </Link>
        <Sheet open={open} onOpenChange={setOpen} modal={false}>
          <SheetTrigger asChild>
            <button aria-label="Open navigation menu" aria-expanded={open}>
              <ICONS.NavbarIcon
                width={25}
                height={21}
                color={COLORS.LIGHT.BLACK}
              />
            </button>
          </SheetTrigger>
          <SheetContent className="bg-background gap-0 overflow-y-auto">
            <SheetHeader className="bg-transparent">
              <SheetTitle className="w-full flex items-center justify-center -mt-1 pb-4 border-b border-muted">
                <Image
                  src="/logo.svg"
                  width={90}
                  height={36}
                  alt="logo"
                  className="w-[90px] h-[36px]"
                />
              </SheetTitle>
            </SheetHeader>
            <Accordion type="single" className="px-4" collapsible>
              <AccordionItem value="bars">
                <AccordionTrigger>
                  <p className="text-xl font-semibold text-left w-full">Bars</p>
                </AccordionTrigger>
                <AccordionContent className="bg-transparent flex flex-col gap-2">
                  {docs?.map((city, idx) => (
                    <Link
                      href={`/${city.slug}/bars`}
                      key={city.id}
                      className={cn(
                        "flex flex-row gap-1 py-2 border-b border-inactive",
                        docs.length - 1 === idx && "border-b-0"
                      )}
                      onClick={() => setOpen(false)}
                    >
                      <p className="font-medium text-sm">Bars in</p>
                      <p className="text-sm font-bold">{city.name}</p>
                    </Link>
                  ))}
                </AccordionContent>
              </AccordionItem>
            </Accordion>
            <Accordion type="single" className="px-4" collapsible>
              <AccordionItem value="clubs">
                <AccordionTrigger>
                  <p className="text-xl font-semibold text-left w-full text-text">
                    Clubs
                  </p>
                </AccordionTrigger>
                <AccordionContent className="bg-transparent flex flex-col gap-2">
                  {docs?.map((city, idx) => (
                    <Link
                      href={`/${city.slug}/clubs`}
                      key={city.id}
                      className={cn(
                        "flex flex-row gap-1 py-2 border-b border-inactive",
                        docs.length - 1 === idx && "border-b-0"
                      )}
                      onClick={() => setOpen(false)}
                    >
                      <p className="font-medium text-sm">Clubs in</p>
                      <p className="text-sm font-bold">{city.name}</p>
                    </Link>
                  ))}
                </AccordionContent>
              </AccordionItem>
            </Accordion>
            <Tooltip>
              <TooltipTrigger className="self-start text-xl font-semibold pl-4 my-3">
                <Link href={""}>Events</Link>
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-sm font-medium text-primary">Coming Soon</p>
              </TooltipContent>
            </Tooltip>
            <Link href={`/contact`} className="text-xl font-semibold pl-4 my-3">
              Contact Us
            </Link>

            <div className="w-full px-4 flex flex-col justify-center gap-10 mt-8">
              <DownloadAppButton />
            </div>
          </SheetContent>
        </Sheet>
      </nav>
    </>
  );
}
